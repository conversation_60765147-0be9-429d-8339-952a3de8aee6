<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AuroraCore Web</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        margin: 0;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .container {
        background: white;
        border-radius: 10px;
        padding: 40px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        text-align: center;
        max-width: 600px;
      }

      .logo {
        width: 100px;
        height: 100px;
        margin: 0 auto 20px;
      }

      h1 {
        color: #2c3e50;
        margin-bottom: 10px;
      }

      .subtitle {
        color: #7f8c8d;
        margin-bottom: 30px;
      }

      .feature-list {
        text-align: left;
        margin: 20px 0;
      }

      .feature-item {
        margin: 10px 0;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 5px;
        border-left: 4px solid #667eea;
      }

      .btn {
        background: #667eea;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        margin: 10px;
        transition: background 0.3s;
      }

      .btn:hover {
        background: #5a6fd8;
      }

      .counter {
        font-size: 24px;
        margin: 20px 0;
        color: #2c3e50;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="container">
        <div class="logo">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 198">
            <path fill="#41B883" d="M204.8 0H256L128 220.8L0 0h97.92L128 51.2L157.44 0h47.36Z"/>
            <path fill="#41B883" d="m0 0l128 220.8L256 0h-51.2L128 132.48L50.56 0H0Z"/>
            <path fill="#35495E" d="M50.56 0L128 133.12L204.8 0h-47.36L128 51.2L97.92 0H50.56Z"/>
          </svg>
        </div>

        <h1>{{ title }}</h1>
        <p class="subtitle">{{ subtitle }}</p>

        <div class="counter">
          计数器: {{ count }}
        </div>

        <button class="btn" @click="increment">点击 +1</button>
        <button class="btn" @click="reset">重置</button>

        <div class="feature-list">
          <div class="feature-item" v-for="feature in features" :key="feature">
            ✨ {{ feature }}
          </div>
        </div>

        <p style="margin-top: 30px; color: #7f8c8d;">
          项目已成功创建！现在您可以开始开发了。
        </p>
      </div>
    </div>

    <!-- Vue 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script>
      const { createApp, ref } = Vue;

      createApp({
        setup() {
          const title = ref('AuroraCore Web');
          const subtitle = ref('基于 Vue 3 的现代化前端项目');
          const count = ref(0);
          const features = ref([
            'Vue 3 Composition API',
            '响应式数据绑定',
            '组件化开发',
            '现代化构建工具支持',
            '热重载开发体验'
          ]);

          const increment = () => {
            count.value++;
          };

          const reset = () => {
            count.value = 0;
          };

          return {
            title,
            subtitle,
            count,
            features,
            increment,
            reset
          };
        }
      }).mount('#app');
    </script>
  </body>
</html>
