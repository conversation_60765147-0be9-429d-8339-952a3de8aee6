# 🎮 12Wave Interactive - 公司介绍互动游戏

## 游戏概述

这是一个专为12Wave公司设计的互动展示游戏，将传统的打砖块游戏转换为一个有趣的公司介绍体验。用户通过控制挡板让球触碰文字，以互动的方式了解12Wave公司。

## 🎯 游戏特色

### 📝 公司介绍内容
游戏展示以下12Wave公司信息：

```
# Contacts

## Get in touch with 12Wave!

At 12Wave, we are always excited about new opportunities to work together! 
Our company is open to inspiring projects and partnerships that can lead to 
mutual success. Send us your ideas and we will be happy to bring them to life!
```

### 🎨 视觉效果
- **手绘风格文字**：所有文字采用Comic Sans MS字体，营造友好氛围
- **动态摆动效果**：文字具有轻微的手绘风格摆动动画
- **颜色变化**：文字被球触碰后从黑色(#000000)变为红色(#e9554d)
- **发光效果**：被触碰的文字具有红色发光效果

### 🎮 游戏机制
- **无通关压力**：这是一个展示型游戏，没有胜负概念
- **触碰计数**：统计用户触碰了多少个单词
- **生命系统**：保留3条生命，增加游戏趣味性
- **重新开始**：随时可以重置游戏状态

## 🛠 技术实现

### 📱 文字布局算法
```javascript
createWords() {
  let x = 50, y = 80
  const lineHeight = 40
  const maxWidth = this.canvasWidth - 100
  
  this.companyText.forEach((word) => {
    // 动态字体大小
    const fontSize = word === 'Contacts' ? 32 : 
                    word.startsWith('Get') ? 24 : 20
    
    // 自动换行
    if (x + wordWidth > maxWidth) {
      x = 50
      y += lineHeight
    }
    
    // 创建文字对象
    this.words.push({
      text: word,
      x: x, y: y,
      fontSize: fontSize,
      color: '#000000',
      touched: false
    })
  })
}
```

### 🎯 碰撞检测
```javascript
ballWordCollision(word) {
  return this.ball.x + this.ball.radius > word.x &&
         this.ball.x - this.ball.radius < word.x + word.width &&
         this.ball.y + this.ball.radius > word.y &&
         this.ball.y - this.ball.radius < word.y + word.height
}
```

### 🎨 颜色变化效果
```javascript
// 球与文字碰撞时
if (!word.touched && this.ballWordCollision(word)) {
  word.touched = true
  word.color = '#e9554d'  // 变为红色
  this.touchedWords += 1
}

// 绘制时添加发光效果
if (word.touched) {
  this.ctx.shadowColor = '#e9554d'
  this.ctx.shadowBlur = 10
  this.ctx.fillText(word.text, 0, 0)
}
```

## 🎮 用户体验

### 🖱 控制方式
- **鼠标控制**：移动鼠标控制挡板位置
- **点击开始**：点击游戏区域开始互动
- **暂停功能**：可以暂停和继续游戏
- **重置功能**：一键重新开始

### 📊 界面信息
- **触碰计数**：显示"触碰单词: X"
- **生命显示**：显示剩余生命数
- **友好提示**：引导用户"让球触碰文字，了解12Wave！"

## 🌐 集成方式

### 🧭 导航栏
- 链接文字：`🎮 12Wave Interactive`
- 路由地址：`/brick-breaker`
- 特殊样式：渐变色彩 + 发光动画

### 📱 响应式设计
- 游戏画布：800x600像素
- 自适应布局：适配不同屏幕尺寸
- 移动端友好：保持良好的触控体验

## 🎯 商业价值

### 💼 品牌展示
- **互动体验**：用户主动参与了解公司信息
- **记忆深刻**：游戏化的方式增强记忆效果
- **友好形象**：展现公司的创新和亲和力

### 📈 用户参与
- **降低门槛**：游戏化降低阅读公司介绍的心理门槛
- **增加停留**：有趣的互动增加用户在网站的停留时间
- **社交传播**：独特的体验容易被用户分享

## 🚀 扩展可能

### 🎨 视觉增强
- [ ] 添加粒子效果
- [ ] 更多动画过渡
- [ ] 自定义主题色彩
- [ ] 背景装饰元素

### 📝 内容扩展
- [ ] 多页面公司信息
- [ ] 团队成员介绍
- [ ] 项目案例展示
- [ ] 服务流程说明

### 🎮 功能增强
- [ ] 音效反馈
- [ ] 触摸控制优化
- [ ] 分享功能
- [ ] 数据统计

## 📋 使用说明

1. **访问游戏**：点击导航栏"🎮 12Wave Interactive"
2. **开始互动**：点击游戏区域开始
3. **控制挡板**：移动鼠标控制挡板
4. **触碰文字**：让球触碰文字，观察颜色变化
5. **了解公司**：通过互动方式阅读公司介绍

这个互动游戏成功地将枯燥的公司介绍转换为有趣的用户体验，体现了12Wave公司的创新精神和用户友好的理念。
