# 🎮 手绘风格打砖块游戏

## 游戏简介

这是一个完全手绘风格的打砖块游戏，集成在企业官网中。游戏采用了独特的手绘视觉效果，包括：

- 🎨 手绘风格的图形和动画
- 🌈 丰富的色彩搭配
- ✨ 流畅的动画效果
- 🎯 经典的打砖块玩法

## 游戏特色

### 🎨 视觉效果
- **手绘风格**：所有游戏元素都采用手绘风格，包括砖块、球、挡板
- **动态效果**：游戏元素具有轻微的摆动和呼吸效果
- **彩色砖块**：6种不同颜色的砖块，视觉丰富
- **特效动画**：球的轨迹、碰撞效果等都有精心设计的动画

### 🎮 游戏玩法
- **经典规则**：移动挡板反弹球，打破所有砖块获胜
- **智能反弹**：根据球击中挡板的位置改变反弹角度
- **生命系统**：3条生命，球掉落会失去生命
- **得分系统**：每打破一个砖块获得10分

### 🎯 控制方式
- **鼠标控制**：移动鼠标控制挡板位置
- **点击开始**：点击游戏区域开始游戏
- **暂停功能**：可以随时暂停和继续游戏
- **重置功能**：可以随时重新开始游戏

## 技术实现

### 🛠 技术栈
- **Vue.js 2.5**：组件化开发
- **Canvas API**：游戏渲染
- **CSS3 动画**：界面动效
- **ES6+**：现代JavaScript语法

### 🎨 手绘效果实现
```javascript
// 手绘风格的圆形绘制
const points = 16
const angleStep = (Math.PI * 2) / points
for (let i = 1; i <= points; i++) {
  const angle = i * angleStep
  const radius = this.ball.radius + Math.sin(angle * 3 + this.wobbleOffset) * 0.8
  const x = Math.cos(angle) * radius
  const y = Math.sin(angle) * radius
  this.ctx.lineTo(x, y)
}
```

### 🎮 游戏逻辑
- **碰撞检测**：精确的球与砖块、挡板碰撞检测
- **物理模拟**：真实的球体运动轨迹
- **状态管理**：完整的游戏状态管理系统

## 文件结构

```
src/components/BrickBreaker.vue    # 主游戏组件
├── template                       # 游戏界面模板
├── script                        # 游戏逻辑
│   ├── 游戏状态管理
│   ├── 碰撞检测系统
│   ├── 渲染引擎
│   └── 用户交互处理
└── style                         # 手绘风格样式
```

## 游戏配置

### 🎯 游戏参数
```javascript
// 砖块配置
brickRows: 6,        // 砖块行数
brickCols: 10,       // 砖块列数
brickWidth: 70,      // 砖块宽度
brickHeight: 25,     // 砖块高度

// 球的配置
ball: {
  radius: 8,         // 球的半径
  dx: 4,            // 水平速度
  dy: -4            // 垂直速度
}

// 挡板配置
paddle: {
  width: 100,       // 挡板宽度
  height: 15        // 挡板高度
}
```

### 🎨 颜色配置
```javascript
const colors = [
  '#FF6B6B',  // 红色
  '#4ECDC4',  // 青色
  '#45B7D1',  // 蓝色
  '#96CEB4',  // 绿色
  '#FFEAA7',  // 黄色
  '#DDA0DD'   // 紫色
]
```

## 访问游戏

### 🌐 在线访问
1. 启动开发服务器：`npm run dev`
2. 访问：`http://localhost:8081/brick-breaker`
3. 或者通过导航栏的"🎮 打砖块"链接

### 🎮 游戏操作
1. **开始游戏**：点击游戏区域
2. **控制挡板**：移动鼠标
3. **暂停游戏**：点击"暂停"按钮
4. **重新开始**：点击"重置游戏"按钮

## 扩展功能

### 🚀 可能的增强
- [ ] 多关卡系统
- [ ] 道具系统（加长挡板、多球等）
- [ ] 音效和背景音乐
- [ ] 排行榜系统
- [ ] 移动端触摸控制
- [ ] 更多手绘风格元素

### 🎨 视觉增强
- [ ] 粒子效果
- [ ] 更多动画过渡
- [ ] 主题切换
- [ ] 自定义颜色方案

## 开发说明

这个游戏完全独立于网站的其他部分，采用了与网站不同的手绘风格设计。游戏的所有视觉元素都是通过Canvas API动态绘制的，确保了流畅的动画效果和独特的手绘风格。

游戏代码结构清晰，易于维护和扩展，可以作为其他小游戏开发的参考模板。
