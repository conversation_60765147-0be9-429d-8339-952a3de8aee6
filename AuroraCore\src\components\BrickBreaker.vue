<template>
  <div class="brick-breaker-container">
    <!-- 装饰性圆点 -->
    <div class="decorative-dots">
      <div class="dot dot-orange-large" style="top: 2%; left: 2%;"></div>
      <div class="dot dot-yellow-medium" style="top: 5%; left: 25%;"></div>
      <div class="dot dot-blue-small" style="top: 8%; right: 30%;"></div>
      <div class="dot dot-orange-medium" style="top: 15%; right: 5%;"></div>
      <div class="dot dot-pink-small" style="top: 25%; left: 8%;"></div>
      <div class="dot dot-blue-medium" style="bottom: 20%; right: 15%;"></div>
      <div class="dot dot-yellow-small" style="bottom: 15%; left: 20%;"></div>
      <div class="dot dot-orange-small" style="bottom: 8%; right: 25%;"></div>
      <div class="dot dot-pink-medium" style="bottom: 5%; left: 40%;"></div>
    </div>

    <!-- 左上角 Logo 区域 -->
    <div class="logo-section">
      <div class="logo-container">
        <span class="logo-number">12</span>
        <span class="logo-text">WAVE</span>
        <div class="logo-subtitle">INTERACTIVE STUDIO</div>
      </div>
    </div>

    <!-- 右上角控制按钮 -->
    <div class="top-controls">
      <button class="control-icon sound-btn">🔊</button>
      <button class="control-icon menu-btn">☰</button>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="content-header">
        <h1 class="main-title">TukTuk（吐壳儿）</h1>
        <h2 class="sub-title">让交互科技融入生活小时刻</h2>
      </div>

      <!-- 游戏区域 -->
      <div class="game-area" ref="gameArea">
        <canvas
          ref="gameCanvas"
          :width="canvasWidth"
          :height="canvasHeight"
          @mousemove="movePaddle"
        ></canvas>

        <div v-if="!gameStarted" class="start-overlay">
          <div class="start-message">
            <div class="play-button-container">
              <button @click="startGame" class="modern-play-btn">
                <div class="play-icon">▶</div>
              </button>
              <div class="play-text">Play...</div>
            </div>
          </div>
        </div>

        <div v-if="gameOver" class="game-over-overlay">
          <div class="game-over-message">
            <h2>Game Over</h2>
            <p>Words touched: {{ touchedWords }}</p>
            <button @click="resetGame" class="restart-btn">Play Again</button>
          </div>
        </div>
      </div>

      <!-- 游戏统计 -->
      <div class="game-stats">
        <div class="stat-item">
          <span class="stat-label">Score</span>
          <span class="stat-value">{{ touchedWords }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Lives</span>
          <span class="stat-value">{{ lives }}</span>
        </div>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="bottom-section">
      <div class="copyright">© 2025 TukTuk（吐壳儿）版权所有</div>
      <div class="social-links">
        <a href="#" class="social-link">✕</a>
        <a href="#" class="social-link">f</a>
        <a href="mailto:<EMAIL>" class="email-link">联系我们！</a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BrickBreaker',
  data() {
    return {
      // Canvas 设置 - 响应式初始化
      canvasWidth: (() => {
        const isMobile = window.innerWidth <= 768
        const padding = isMobile ? 40 : 100
        const maxWidth = isMobile ? 800 : 1000
        return Math.min(maxWidth, window.innerWidth - padding)
      })(),
      canvasHeight: (() => {
        const isMobile = window.innerWidth <= 768
        const maxHeight = isMobile ? 500 : 700
        return Math.min(maxHeight, window.innerHeight - (isMobile ? 350 : 200))
      })(),
      ctx: null,

      // 游戏状态
      gameStarted: false,
      gameOver: false,
      paused: false,
      touchedWords: 0,
      lives: 3,
      
      // 游戏对象
      ball: {
        x: 400,
        y: (() => {
          const isMobile = window.innerWidth <= 768
          const maxHeight = isMobile ? 500 : 700
          const canvasHeight = Math.min(maxHeight, window.innerHeight - (isMobile ? 300 : 200))
          return canvasHeight - 80 // 球在挡板上方50像素
        })(),
        dx: 4,
        dy: -4,
        radius: 8
      },
      
      paddle: {
        x: 350,
        y: (() => {
          const isMobile = window.innerWidth <= 768
          const maxHeight = isMobile ? 500 : 700
          const canvasHeight = Math.min(maxHeight, window.innerHeight - (isMobile ? 300 : 200))
          return canvasHeight - 30 // 挡板距离底部30像素
        })(),
        width: 100,
        height: 15
      },
      
      words: [],

      // 文字设置
      companyText: [
        'TukTuk（吐壳儿）创立于2025年5月，总部位于河北沧州，是专注于科技化创意礼物的创新型品牌。',
        '我们通过智能技术与情感化设计的融合，为年轻消费者开发兼具趣味性与实用性的礼物产品，',
        '让科技成为传递心意的温暖载体。以"创新、诚信、合作、卓越"为核心价值观，',
        'TukTuk致力于降低科技体验门槛，让一线至三四线城市的年轻群体均能享受科技赋予生活的惊喜感。',
        '未来，我们将持续探索技术与人情的共生关系，助力平凡日常绽放愉悦光芒。'
      ],
      
      // 动画
      animationId: null,
      
      // 手绘效果
      wobbleOffset: 0
    }
  },
  
  mounted() {
    this.updateCanvasSize()
    this.initGame()
    window.addEventListener('resize', this.updateCanvasSize)
  },

  beforeUnmount() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
    }
    window.removeEventListener('resize', this.updateCanvasSize)
  },
  
  methods: {
    updateCanvasSize() {
      // 响应式canvas尺寸计算
      const isMobile = window.innerWidth <= 768
      const padding = isMobile ? 40 : 100
      const maxWidth = isMobile ? 800 : 800 // PC端也限制为800，确保文字排列一致
      const maxHeight = isMobile ? 500 : 700

      this.canvasWidth = Math.min(maxWidth, window.innerWidth - padding)
      this.canvasHeight = Math.min(maxHeight, window.innerHeight - (isMobile ? 350 : 200))

      // 如果canvas已经存在，重新初始化游戏元素
      if (this.$refs.gameCanvas && this.ctx) {
        // 更新挡板位置
        this.paddle.y = this.canvasHeight - 30
        this.paddle.x = (this.canvasWidth - this.paddle.width) / 2
        this.resetBall()
        this.createWords()
        this.draw()
      }
    },

    initGame() {
      const canvas = this.$refs.gameCanvas
      if (!canvas) {
        console.warn('Canvas not found, retrying...')
        this.$nextTick(() => {
          this.initGame()
        })
        return
      }

      this.ctx = canvas.getContext('2d')
      if (!this.ctx) {
        console.error('Failed to get canvas context')
        return
      }

      this.createWords()
      this.draw()

      // 开始文字动画循环（即使游戏未开始也要显示文字动画）
      this.startTextAnimation()
    },

    startTextAnimation() {
      // 文字现在是静态的，只需要绘制一次
      if (!this.gameStarted) {
        this.draw()
      }
    },
    
    createWords() {
      this.words = []
      const isMobile = window.innerWidth <= 768
      let x = 0 // 从canvas左边缘开始
      let y = isMobile ? 25 : 30 // 手机版起始位置更靠上
      const lineHeight = isMobile ? 28 : 50 // 手机版使用更小的行高
      const maxWidth = this.canvasWidth // 使用完整的canvas宽度

      // 检查context是否可用
      if (!this.ctx) {
        console.warn('Canvas context not available for createWords')
        return
      }

      // 将所有句子合并并按字符分割
      const allText = this.companyText.join('')
      const characters = allText.split('')

      characters.forEach((char) => {
        // 跳过空格和换行符
        if (char === ' ' || char === '\n') {
          const spacing = isMobile ? 6 : 12
          x += spacing
          return
        }

        // 响应式字体大小
        const fontSize = isMobile ? 16 : 28 // 手机版使用16px，PC版使用28px
        const fontWeight = 'normal'

        // 设置字体并测量文字宽度
        this.ctx.font = `${fontWeight} ${fontSize}px "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", sans-serif`
        const textWidth = this.ctx.measureText(char).width // 文字本身的宽度
        const spacing = isMobile ? 6 : 12 // 中文字符间距更小
        const charWidth = textWidth + spacing // 总宽度（文字+间距）

        // 检查是否需要换行
        if (x + textWidth >= maxWidth) {
          x = 0 // 从左边缘开始新行
          y += lineHeight
        }

        this.words.push({
          text: char,
          x: x,
          y: y,
          width: textWidth, // 使用文字本身的宽度
          height: fontSize + 5,
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: '#2c3e50',
          touched: false
        })

        x += charWidth // 移动位置时使用包含间距的宽度
      })
    },
    
    startGame() {
      console.log('Play button clicked!', { gameStarted: this.gameStarted, gameOver: this.gameOver })

      // 确保canvas已经初始化
      if (!this.ctx) {
        console.warn('Canvas not ready, initializing...')
        this.initGame()
        if (!this.ctx) {
          console.error('Failed to initialize canvas')
          return
        }
      }

      if (!this.gameStarted && !this.gameOver) {
        this.gameStarted = true
        // 重置球的位置到中心
        this.ball.x = this.canvasWidth / 2
        this.ball.y = this.paddle.y - 50 // 球在挡板上方50像素
        this.ball.dx = 4
        this.ball.dy = -4
        // 重置挡板位置
        this.paddle.x = (this.canvasWidth - this.paddle.width) / 2
        console.log('Game started! Ball position:', this.ball)
        this.gameLoop()
      }
    },
    
    gameLoop() {
      if (!this.paused && !this.gameOver && this.ctx) {
        this.update()
        this.draw()
        this.animationId = requestAnimationFrame(this.gameLoop)
      }
    },
    
    update() {
      // 更新球的位置
      this.ball.x += this.ball.dx
      this.ball.y += this.ball.dy
      
      // 球与墙壁碰撞 - 确保球严格在canvas内
      if (this.ball.x + this.ball.radius >= this.canvasWidth || this.ball.x - this.ball.radius <= 0) {
        this.ball.dx = -this.ball.dx
      }
      
      if (this.ball.y - this.ball.radius < 0) {
        this.ball.dy = -this.ball.dy
      }
      
      // 球掉落 - 如果球跑到挡板下面就死亡
      if (this.ball.y + this.ball.radius > this.paddle.y + this.paddle.height) {
        this.lives--
        if (this.lives <= 0) {
          this.gameOver = true
          this.won = false
        } else {
          this.resetBall()
        }
      }
      
      // 球与挡板碰撞
      if (this.ball.y + this.ball.radius > this.paddle.y &&
          this.ball.x > this.paddle.x &&
          this.ball.x < this.paddle.x + this.paddle.width) {
        // 根据击中位置改变角度
        const hitPos = (this.ball.x - this.paddle.x) / this.paddle.width
        const angle = (hitPos - 0.5) * Math.PI / 3
        const speed = Math.sqrt(this.ball.dx * this.ball.dx + this.ball.dy * this.ball.dy)
        this.ball.dx = Math.sin(angle) * speed
        this.ball.dy = -Math.abs(Math.cos(angle) * speed)
      }
      
      // 球与文字碰撞
      for (let word of this.words) {
        if (!word.touched && this.ballWordCollision(word)) {
          this.ball.dy = -this.ball.dy
          word.touched = true
          word.color = '#e9554d'
          this.touchedWords += 1
          break
        }
      }
      
      // 更新手绘效果
      this.wobbleOffset += 0.1
    },

    draw() {
      if (!this.ctx) {
        console.warn('Canvas context not available')
        return
      }

      // 清空画布
      this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)

      // 绘制背景
      this.drawBackground()

      // 绘制文字
      this.drawWords()

      // 绘制挡板
      this.drawPaddle()

      // 绘制球
      this.drawBall()
    },

    drawBackground() {
      // 背景现在是透明的，不绘制任何边框
    },

    drawWords() {
      for (let word of this.words) {
        this.ctx.save()

        // 设置文字位置（移除摆动效果）
        this.ctx.translate(word.x, word.y)

        // 设置字体
        this.ctx.font = `${word.fontWeight} ${word.fontSize}px Inter, sans-serif`
        this.ctx.fillStyle = word.color
        this.ctx.textAlign = 'left'
        this.ctx.textBaseline = 'top'

        // 绘制文字
        this.ctx.fillText(word.text, 0, 0)

        // 如果被触碰，添加发光效果
        if (word.touched) {
          this.ctx.shadowColor = '#e9554d'
          this.ctx.shadowBlur = 8
          this.ctx.shadowOffsetX = 0
          this.ctx.shadowOffsetY = 0
          this.ctx.fillText(word.text, 0, 0)
          this.ctx.shadowBlur = 0
        }

        this.ctx.restore()
      }
    },

    drawPaddle() {
      this.ctx.save()

      // 手绘风格的挡板
      const wobble = Math.sin(this.wobbleOffset * 2) * 1
      this.ctx.translate(this.paddle.x + wobble, this.paddle.y)
      this.ctx.rotate(Math.sin(this.wobbleOffset * 0.3) * 0.02)

      // 挡板主体 - 现代渐变风格
      const gradient = this.ctx.createLinearGradient(0, 0, 0, this.paddle.height)
      gradient.addColorStop(0, '#667eea')
      gradient.addColorStop(1, '#764ba2')
      this.ctx.fillStyle = gradient
      this.ctx.fillRect(0, 0, this.paddle.width, this.paddle.height)

      // 现代边框
      this.ctx.strokeStyle = 'rgba(44, 62, 80, 0.3)'
      this.ctx.lineWidth = 2
      this.ctx.strokeRect(0, 0, this.paddle.width, this.paddle.height)

      // 高光效果
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)'
      this.ctx.fillRect(2, 2, this.paddle.width - 4, 4)

      this.ctx.restore()
    },

    drawBall() {
      this.ctx.save()

      // 手绘风格的球
      const wobble = Math.sin(this.wobbleOffset * 3) * 0.5
      this.ctx.translate(this.ball.x + wobble, this.ball.y + wobble)

      // 球的主体 - 现代渐变风格
      const ballGradient = this.ctx.createRadialGradient(-2, -2, 0, 0, 0, this.ball.radius)
      ballGradient.addColorStop(0, '#667eea')
      ballGradient.addColorStop(0.7, '#764ba2')
      ballGradient.addColorStop(1, '#2c3e50')

      this.ctx.fillStyle = ballGradient
      this.ctx.beginPath()
      this.ctx.arc(0, 0, this.ball.radius, 0, Math.PI * 2)
      this.ctx.fill()

      // 球的边框
      this.ctx.strokeStyle = 'rgba(44, 62, 80, 0.4)'
      this.ctx.lineWidth = 1.5
      this.ctx.stroke()

      // 高光
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
      this.ctx.beginPath()
      this.ctx.arc(-2, -2, 2.5, 0, Math.PI * 2)
      this.ctx.fill()

      this.ctx.restore()
    },

    ballWordCollision(word) {
      return this.ball.x + this.ball.radius > word.x &&
             this.ball.x - this.ball.radius < word.x + word.width &&
             this.ball.y + this.ball.radius > word.y &&
             this.ball.y - this.ball.radius < word.y + word.height
    },

    movePaddle(event) {
      if (this.gameStarted && !this.gameOver && !this.paused) {
        const rect = this.$refs.gameCanvas.getBoundingClientRect()
        // 计算鼠标在canvas内的相对位置，考虑canvas的缩放
        const scaleX = this.canvasWidth / rect.width
        const mouseX = (event.clientX - rect.left) * scaleX

        // 挡板移动范围应该与文字模块宽度一致（从0到canvasWidth）
        const minX = 0
        const maxX = this.canvasWidth - this.paddle.width
        this.paddle.x = Math.max(minX, Math.min(mouseX - this.paddle.width / 2, maxX))
      }
    },

    resetBall() {
      this.ball.x = this.canvasWidth / 2
      this.ball.y = this.paddle.y - 50 // 球在挡板上方50像素
      this.ball.dx = (Math.random() - 0.5) * 8
      this.ball.dy = -4
    },

    pauseGame() {
      this.paused = !this.paused
      if (!this.paused) {
        this.gameLoop()
      }
    },

    resetGame() {
      this.gameStarted = false
      this.gameOver = false
      this.paused = false
      this.touchedWords = 0
      this.lives = 3

      this.resetBall()
      this.paddle.x = (this.canvasWidth - this.paddle.width) / 2

      // 确保canvas已经初始化再创建文字和绘制
      if (this.ctx) {
        this.createWords()
        this.draw()
        // 重新开始文字动画
        this.startTextAnimation()
      } else {
        this.initGame()
      }

      if (this.animationId) {
        cancelAnimationFrame(this.animationId)
      }
    }
  }
}
</script>

<style scoped>
.brick-breaker-container {
  width: 100%;
  height: 100vh;
  background: url('../assets/bg.jpg') center center / cover no-repeat;
  position: relative;
  overflow: hidden;
  font-family: 'Arial', sans-serif;
  display: flex;
  flex-direction: column;
}

/* 装饰性圆点 */
.decorative-dots {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.dot {
  position: absolute;
  border-radius: 50%;
  opacity: 0.8;
}

.dot-orange-large { width: 60px; height: 60px; background: #ff6b47; }
.dot-orange-medium { width: 40px; height: 40px; background: #ff8566; }
.dot-orange-small { width: 20px; height: 20px; background: #ff9980; }
.dot-yellow-medium { width: 35px; height: 35px; background: #ffd93d; }
.dot-yellow-small { width: 15px; height: 15px; background: #ffe066; }
.dot-blue-medium { width: 45px; height: 45px; background: #4a90e2; }
.dot-blue-small { width: 25px; height: 25px; background: #6ba3f0; }
.dot-pink-medium { width: 30px; height: 30px; background: #ff6b9d; }
.dot-pink-small { width: 18px; height: 18px; background: #ff85b3; }

/* Logo 区域 */
.logo-section {
  position: absolute;
  top: 30px;
  left: 30px;
  z-index: 10;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.logo-number {
  font-size: 3rem;
  font-weight: bold;
  color: #ff6b47;
  line-height: 0.8;
}

.logo-text {
  font-size: 1.8rem;
  font-weight: bold;
  color: #333;
  letter-spacing: 2px;
  margin-top: -5px;
}

.logo-subtitle {
  font-size: 0.7rem;
  color: #666;
  letter-spacing: 1px;
  margin-top: 2px;
}

/* 顶部控制按钮 */
.top-controls {
  position: absolute;
  top: 30px;
  right: 30px;
  display: flex;
  gap: 15px;
  z-index: 10;
}

.control-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #333;
  color: white;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-icon:hover {
  transform: scale(1.1);
  background: #555;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 5;
  padding: 0 50px;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.content-header {
  text-align: center;
  margin-bottom: 40px;
}

.main-title {
  font-size: 4rem;
  font-weight: bold;
  color: #333;
  margin: 0 0 10px 0;
  letter-spacing: -2px;
}

.sub-title {
  font-size: 1.5rem;
  color: #666;
  margin: 0;
  font-weight: normal;
}

.game-title {
  font-size: 2.8rem;
  color: #2c3e50;
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.02em;
  text-shadow: none;
}

.game-stats {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 15px;
}

.game-stats span {
  background: rgba(255, 255, 255, 0.9);
  padding: 10px 20px;
  border-radius: 25px;
  border: 2px solid rgba(44, 62, 80, 0.2);
  font-weight: 600;
  font-size: 1rem;
  color: #2c3e50;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.game-area {
  position: relative;
  border: none;
  border-radius: 0;
  background: transparent;
  box-shadow: none;
  overflow: hidden;
  backdrop-filter: none;
  margin-bottom: 30px;
  width: auto; /* 改为auto，让宽度由canvas决定 */
  max-width: none; /* 移除最大宽度限制 */
  display: flex;
  justify-content: center; /* 居中显示 */
}

/* 游戏统计样式 */
.game-stats {
  display: flex;
  gap: 30px;
  justify-content: center;
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.stat-value {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  color: #333;
}

canvas {
  display: block;
  cursor: none;
  /* 移除width和height的CSS设置，让canvas使用其原生尺寸 */
}

.start-overlay,
.game-over-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  text-align: center;
  backdrop-filter: none;
}

.start-message h2,
.game-over-message h2 {
  font-size: 2.2rem;
  margin-bottom: 20px;
  color: #333;
  font-weight: 700;
  letter-spacing: -0.02em;
}

.start-message p {
  font-size: 1.1rem;
  margin: 8px 0;
  color: #666;
  font-weight: 400;
}

.game-over-message p {
  color: #666;
}

.play-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 50px;
  padding: 18px 35px;
  font-size: 1.3rem;
  font-weight: 600;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 25px auto 0;
  font-family: 'Inter', sans-serif;
  letter-spacing: 0.5px;
}

.play-btn:hover {
  transform: translateY(-2px);
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.play-icon {
  font-size: 1.2rem;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.restart-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 25px;
  padding: 12px 25px;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
  font-family: 'Inter', sans-serif;
}

.restart-btn:hover {
  transform: translateY(-2px);
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.game-controls {
  display: flex;
  gap: 15px;
  margin-top: 25px;
}

.control-btn {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(44, 62, 80, 0.2);
  border-radius: 25px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  font-family: 'Inter', sans-serif;
}

.control-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 现代播放按钮样式 */
.play-button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.modern-play-btn {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #333;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modern-play-btn:hover {
  transform: scale(1.1);
  background: #555;
}

.modern-play-btn .play-icon {
  color: white;
  font-size: 2rem;
  margin-left: 5px;
}

.play-text {
  font-size: 1.2rem;
  color: #333;
  font-weight: 500;
}

/* 底部区域 */
.bottom-section {
  position: absolute;
  bottom: 30px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 50px;
  z-index: 10;
}

.copyright {
  font-size: 0.8rem;
  color: #666;
}

.social-links {
  display: flex;
  gap: 15px;
  align-items: center;
}

.social-link {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #333;
  color: white;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  transition: all 0.3s ease;
}

.social-link:hover {
  transform: scale(1.1);
  background: #555;
}

.email-link {
  background: #4CAF50;
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.email-link:hover {
  background: #45a049;
  transform: translateY(-2px);
}

/* 手机版响应式设计 */
@media (max-width: 768px) {
  .brick-breaker-container {
    padding: 0;
  }

  /* Logo 区域 - 手机版 */
  .logo-section {
    top: 20px;
    left: 20px;
  }

  .logo-number {
    font-size: 2rem;
  }

  .logo-text {
    font-size: 1.2rem;
  }

  .logo-subtitle {
    font-size: 0.6rem;
  }

  /* 顶部控制按钮 - 手机版 */
  .top-controls {
    top: 20px;
    right: 20px;
    gap: 10px;
  }

  .control-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  /* 主要内容区域 - 手机版 */
  .main-content {
    padding: 0 20px;
    margin-top: 80px;
  }

  .content-header {
    margin-bottom: 30px;
  }

  .main-title {
    font-size: 2.5rem;
    letter-spacing: -1px;
  }

  .sub-title {
    font-size: 1.2rem;
  }

  /* 游戏区域 - 手机版 */
  .game-area {
    width: auto; /* 改为auto，让宽度由canvas决定 */
    max-width: none; /* 移除最大宽度限制 */
    margin-bottom: 20px;
    justify-content: center; /* 确保居中 */
  }

  /* 游戏统计 - 手机版 */
  .game-stats {
    gap: 20px;
    margin-bottom: 30px;
    margin-top: 20px;
  }

  .stat-label {
    font-size: 0.8rem;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  /* 播放按钮 - 手机版 */
  .modern-play-btn {
    width: 60px;
    height: 60px;
  }

  .modern-play-btn .play-icon {
    font-size: 1.5rem;
  }

  .play-text {
    font-size: 1rem;
  }

  /* 底部区域 - 手机版 */
  .bottom-section {
    bottom: 15px;
    padding: 0 20px;
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .copyright {
    font-size: 0.7rem;
  }

  .social-links {
    gap: 10px;
  }

  .social-link {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }

  .email-link {
    padding: 8px 16px;
    font-size: 0.8rem;
  }

  /* 装饰圆点 - 手机版调整 */
  .dot-orange-large { width: 40px; height: 40px; }
  .dot-orange-medium { width: 30px; height: 30px; }
  .dot-blue-medium { width: 35px; height: 35px; }
  .dot-yellow-medium { width: 25px; height: 25px; }
  .dot-pink-medium { width: 20px; height: 20px; }
}

/* 小屏手机版 */
@media (max-width: 480px) {
  .main-title {
    font-size: 2rem;
  }

  .sub-title {
    font-size: 1rem;
  }

  .logo-number {
    font-size: 1.5rem;
  }

  .logo-text {
    font-size: 1rem;
  }

  .main-content {
    padding: 0 15px;
    margin-top: 70px;
  }

  .bottom-section {
    padding: 0 15px;
  }

  .game-stats {
    gap: 15px;
    margin-bottom: 25px;
  }

  .stat-value {
    font-size: 1.2rem;
  }
}
</style>
