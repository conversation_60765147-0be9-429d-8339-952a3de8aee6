<template>
  <div class="home-container" @click="handleClick" :class="{ 'clicked': isClicked, 'loading': isLoading }">
    <!-- 文字圆圈 -->
    <div class="text-circle" :class="{ 'animate-out': isClicked, 'circle-formed': circleFormed }">
      <div class="circle-text">
        <span v-for="(char, index) in circleText" :key="index"
              :style="getCharStyle(index)"
              :class="['char', { 'char-animate-in': showChars }]">
          {{ char }}
        </span>
      </div>
    </div>

    <!-- 绝对居中的加载进度 -->
    <div class="progress-overlay" v-if="showLoading">
      <div class="progress-text">{{ Math.round(progress) }}%</div>
    </div>

    <!-- 点击波纹特效 -->
    <div v-if="showRipple" class="ripple-effect" :style="rippleStyle"></div>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      circleText: '正在为你筹备美好时光'.split(''),
      isClicked: false,
      showRipple: false,
      showChars: false,
      circleFormed: false,
      rippleStyle: {},
      isLoading: true,
      showLoading: false,
      progress: 1,
      loadingComplete: false
    }
  },
  mounted() {
    // 页面加载后延迟触发文字飞入动画
    setTimeout(() => {
      this.showChars = true
    }, 500)

    // 文字飞入完成后显示圆圈形成特效
    setTimeout(() => {
      this.circleFormed = true
      // 圆圈形成后开始显示加载进度
      setTimeout(() => {
        this.showLoading = true
        this.startLoading()
      }, 500)
    }, 500 + this.circleText.length * 100 + 1200) // 等待所有文字飞入完成

    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    getCharStyle(index) {
      const totalChars = this.circleText.length
      const angle = (index * 360) / totalChars
      const delay = index * 0.1 // 每个字符延迟0.1秒

      // 响应式圆圈半径计算
      let radius = 175 // 默认半径
      if (window.innerWidth <= 360) {
        radius = 100
      } else if (window.innerWidth <= 480) {
        radius = 120
      } else if (window.innerWidth <= 768) {
        radius = 140
      }

      // 计算飞入起始位置（从页面四个边缘）
      const edges = ['top', 'right', 'bottom', 'left']
      const edge = edges[index % 4] // 循环使用四个边缘

      let startX = 0
      let startY = 0

      switch (edge) {
        case 'top':
          startX = (Math.random() - 0.5) * window.innerWidth // 顶部边缘的随机X位置
          startY = -200 // 从顶部外侧
          break
        case 'right':
          startX = window.innerWidth + 200 // 从右侧外侧
          startY = (Math.random() - 0.5) * window.innerHeight // 右侧边缘的随机Y位置
          break
        case 'bottom':
          startX = (Math.random() - 0.5) * window.innerWidth // 底部边缘的随机X位置
          startY = window.innerHeight + 200 // 从底部外侧
          break
        case 'left':
          startX = -200 // 从左侧外侧
          startY = (Math.random() - 0.5) * window.innerHeight // 左侧边缘的随机Y位置
          break
      }

      return {
        transform: `rotate(${angle}deg)`,
        transformOrigin: `0 ${radius}px`,
        '--start-x': `${startX}px`,
        '--start-y': `${startY}px`,
        '--delay': `${delay}s`,
        '--final-angle': `${angle}deg`,
        '--edge': edge
      }
    },

    handleResize() {
      // 强制重新渲染以应用新的样式
      this.$forceUpdate()
    },

    startLoading() {
      const duration = 3000 // 3秒完成加载
      const startTime = Date.now()

      const updateProgress = () => {
        const elapsed = Date.now() - startTime
        const progressPercent = Math.min((elapsed / duration) * 100, 100)

        this.progress = progressPercent

        if (progressPercent < 100) {
          requestAnimationFrame(updateProgress)
        } else {
          // 加载完成，但保持显示100%
          this.loadingComplete = true
          this.isLoading = false
          // 不隐藏showLoading，保持100%显示
        }
      }

      requestAnimationFrame(updateProgress)
    },

    handleClick(event) {
      // 如果还在加载中，禁止点击
      if (this.isLoading || !this.loadingComplete) {
        return
      }
      if (this.isClicked) return // 防止重复点击

      // 获取点击位置
      const rect = this.$el.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top

      // 设置波纹效果位置
      this.rippleStyle = {
        left: x + 'px',
        top: y + 'px'
      }

      // 显示波纹效果
      this.showRipple = true
      this.isClicked = true

      // 点击时隐藏加载进度
      this.showLoading = false

      // 延迟跳转，让特效播放完成
      setTimeout(() => {
        this.$router.push('/second')
      }, 800)
    }
  }
}
</script>

<style scoped>
/* 引入自定义字体 */
@font-face {
  font-family: 'DemoSlantBlack';
  src: url('../assets/演示斜黑体.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
}

.home-container {
  width: 100%;
  height: 100vh;
  background-color: #7bb3d3;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  transition: all 0.8s ease;
}

.home-container.clicked {
  background-color: rgb(227, 94, 54);
}

.home-container.loading {
  cursor: not-allowed;
}

.text-circle {
  position: relative;
  width: 350px;
  height: 350px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.circle-text {
  position: relative;
  width: 100%;
  height: 100%;
  /* 延迟开始旋转，等文字飞入完成 */
  animation: rotate 20s linear infinite;
  animation-delay: 2s; /* 等待文字飞入完成后开始旋转 */
  transition: all 0.6s ease;
}

.text-circle.animate-out {
  transform: scale(1.2);
  opacity: 0;
}

.text-circle.circle-formed {
  animation: circleFormPulse 2s ease-in-out;
}

@keyframes circleFormPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 加载中心样式 */
.loading-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.5s ease;
}

.loading-center.show {
  opacity: 1;
}

.progress-circle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle {
  stroke-dasharray: 339.292;
  stroke-dashoffset: 339.292;
  transition: stroke-dashoffset 0.1s ease;
  stroke-linecap: round;
  filter: drop-shadow(0 0 8px rgba(244, 242, 235, 0.6));
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #f4f2eb;
  font-size: 24px;
  font-weight: bold;
  font-family: 'DemoSlantBlack', 'PingFang SC', 'Microsoft YaHei', 'SimHei', sans-serif;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* 绝对居中的进度覆盖层 */
.progress-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  pointer-events: none;
}

.progress-text {
  color: #f4f2eb;
  font-size: 32px;
  font-weight: bold;
  font-family: 'DemoSlantBlack', 'PingFang SC', 'Microsoft YaHei', 'SimHei', sans-serif;
  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.5);
  animation: progressPulse 1.5s ease-in-out infinite;
  line-height: 1;
  margin: 0;
  padding: 0;
}

@keyframes progressPulse {
  0%, 100% {
    transform: scale(1);
    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.5);
  }
  50% {
    transform: scale(1.1);
    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.5),
                 0 0 20px rgba(244, 242, 235, 0.8),
                 0 0 40px rgba(244, 242, 235, 0.6);
  }
}

/* 波纹特效 */
.ripple-effect {
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: translate(-50%, -50%);
  animation: ripple 0.8s ease-out;
  pointer-events: none;
}

.char {
  position: absolute;
  left: 50%;
  top: 0;
  transform-origin: 0 175px;
  color: #f4f2eb;
  font-size: 48px;
  font-weight: normal;
  font-family: 'DemoSlantBlack', 'PingFang SC', 'Microsoft YaHei', 'SimHei', sans-serif;

  /* 初始状态：在页面边缘外 */
  opacity: 0;
  transform: translate(calc(var(--start-x) - 50vw), calc(var(--start-y) - 50vh)) rotate(var(--final-angle)) scale(0);
  transition: all 1.5s ease-out;
  transition-delay: var(--delay);
}

.char.char-animate-in {
  /* 最终状态：在圆圈上的正确位置 */
  opacity: 1;
  transform: rotate(var(--final-angle)) scale(1);
}

/* 文字发光动画 */
@keyframes charGlow {
  0% {
    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
    transform: rotate(var(--final-angle)) scale(1);
  }
  30% {
    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4),
                 0 0 25px rgba(244, 242, 235, 0.9),
                 0 0 50px rgba(244, 242, 235, 0.7);
    transform: rotate(var(--final-angle)) scale(1.2);
  }
  70% {
    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4),
                 0 0 15px rgba(244, 242, 235, 0.6),
                 0 0 30px rgba(244, 242, 235, 0.4);
    transform: rotate(var(--final-angle)) scale(1.1);
  }
  100% {
    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
    transform: rotate(var(--final-angle)) scale(1);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes ripple {
  0% {
    width: 20px;
    height: 20px;
    opacity: 1;
  }
  100% {
    width: 300px;
    height: 300px;
    opacity: 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .text-circle {
    width: 280px;
    height: 280px;
  }

  .char {
    transform-origin: 0 140px;
    font-size: 38px;
  }

  .progress-text {
    font-size: 26px;
  }
}

@media (max-width: 480px) {
  .text-circle {
    width: 240px;
    height: 240px;
  }

  .char {
    transform-origin: 0 120px;
    font-size: 30px;
  }

  .progress-text {
    font-size: 22px;
  }
}

@media (max-width: 360px) {
  .text-circle {
    width: 200px;
    height: 200px;
  }

  .char {
    transform-origin: 0 100px;
    font-size: 24px;
  }

  .progress-text {
    font-size: 18px;
  }
}
</style>
