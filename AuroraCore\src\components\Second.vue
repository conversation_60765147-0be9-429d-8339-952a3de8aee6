<template>
  <div class="second-container" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd" @wheel="handleWheel">
    <!-- 顶部Logo -->
    <div class="logo-container">
      <img src="../assets/logo.png" alt="Logo" class="logo-image" />
    </div>

    <!-- 飘动图片 -->
    <div class="floating-images">
      <img src="../assets/1.png" alt="Float 1" class="float-img float-img-1" />
      <img src="../assets/2.png" alt="Float 2" class="float-img float-img-2" />
      <img src="../assets/3.png" alt="Float 3" class="float-img float-img-3" />
      <img src="../assets/4.png" alt="Float 4" class="float-img float-img-4" />
      <img src="../assets/5.png" alt="Float 5" class="float-img float-img-5" />
      <img src="../assets/6.png" alt="Float 6" class="float-img float-img-6" />
      <img src="../assets/7.png" alt="Float 7" class="float-img float-img-7" />
      <img src="../assets/8.png" alt="Float 8" class="float-img float-img-8" />
    </div>

    <!-- 时钟图片 -->
    <div class="clock-container" :class="{ 'animate-in': showClock }">
      <img :src="clockImage" alt="Clock" class="clock-image" />
      <!-- 闪动文字 -->
      <div class="clock-text">拨动指针到你心里的位置</div>
      <!-- 下滑提示 -->
      <div class="slide-hint" :class="{ 'show': showSlideHint }">
        <div class="slide-arrow">↓</div>
        <div class="slide-text">向下滑动</div>
      </div>
    </div>

    <!-- 底部图片 -->
    <div class="bottom-image">
      <img src="../assets/end-logo.png" alt="End Logo" class="end-logo" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'Second',
  data() {
    return {
      showClock: false,
      clockImage: require('../assets/clock-one.png'),
      showSlideHint: false,
      touchStartY: 0,
      touchEndY: 0,
      isSliding: false
    }
  },
  mounted() {
    // 页面加载后延迟触发动画
    setTimeout(() => {
      this.showClock = true
    }, 300)

    // 5秒后切换到clock-two.png
    setTimeout(() => {
      this.clockImage = require('../assets/clock-two.png')
    }, 5000)

    // 5.5秒后显示下滑提示
    setTimeout(() => {
      this.showSlideHint = true
    }, 5500)
  },
  methods: {
    // 触摸开始
    handleTouchStart(event) {
      this.touchStartY = event.touches[0].clientY
      this.isSliding = false
    },

    // 触摸移动
    handleTouchMove(event) {
      event.preventDefault() // 防止页面滚动
    },

    // 触摸结束
    handleTouchEnd(event) {
      if (this.isSliding) return

      this.touchEndY = event.changedTouches[0].clientY
      const deltaY = this.touchStartY - this.touchEndY

      // 向下滑动超过50px就跳转
      if (deltaY > 50) {
        this.slideToThird()
      }
    },

    // 鼠标滚轮事件（PC端）
    handleWheel(event) {
      if (this.isSliding) return

      // 向下滚动
      if (event.deltaY > 0) {
        this.slideToThird()
      }
    },

    // 滑动到第三页
    slideToThird() {
      if (this.isSliding) return

      this.isSliding = true
      this.showSlideHint = false // 隐藏提示

      // 添加滑动动画效果
      setTimeout(() => {
        this.$router.push('/third')
      }, 300)
    }
  }
}
</script>
        '--speed': `${speed}s`,
        '--random-offset': `${Math.random() * 20 - 10}px`
      }
    },

    handleClick() {
      if (this.isClicked) return // 防止重复点击

      this.isClicked = true

      // 延迟跳转，让用户看到点击效果
      setTimeout(() => {
        this.$router.push('/third')
      }, 500)
    }
  }
}

<style scoped>
.second-container {
  width: 100%;
  height: 100vh;
  background-color: rgb(227, 94, 54);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* Logo样式 */
.logo-container {
  position: absolute;
  top: 2rem;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 10;
}

.logo-image {
  width: 100%;
  height: auto;
  object-fit: contain;
}

.clock-container {
  position: relative;
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: translateX(-100vw) scale(0.3) rotate(-360deg);
  opacity: 0;
  transition: all 2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.clock-container.animate-in {
  transform: translateX(0) scale(1) rotate(0deg);
  opacity: 1;
}

.clock-image {
  width: 60vw;
  height: 60vw;
  max-width: 800px;
  max-height: 800px;
  object-fit: contain;
  transform-style: preserve-3d;
  perspective: 1000px;
  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3))
          drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2))
          drop-shadow(0 0 30px rgba(244, 242, 235, 0.4));
  animation: float3D 6s ease-in-out infinite;
  transition: all 0.3s ease;
}

.clock-image:hover {
  transform: rotateY(15deg) rotateX(5deg) scale(1.05);
  filter: drop-shadow(0 30px 60px rgba(0, 0, 0, 0.4))
          drop-shadow(0 15px 30px rgba(0, 0, 0, 0.3))
          drop-shadow(0 0 50px rgba(244, 242, 235, 0.6));
}

/* 闪动文字样式 */
.clock-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 2rem;
  font-family: 'Arial', 'Helvetica', sans-serif;
  font-weight: bold;
  text-align: center;
  opacity: 0.8;
  animation: textBlink 3s ease-in-out infinite;
  z-index: 110;
  white-space: nowrap;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  pointer-events: none;
}

/* 下滑提示样式 */
.slide-hint {
  position: fixed;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  font-family: 'Arial', 'Helvetica', sans-serif;
  opacity: 0;
  transition: all 0.5s ease;
  z-index: 120;
}

.slide-hint.show {
  opacity: 0.9;
}

.slide-arrow {
  font-size: 3rem;
  animation: slideArrowBounce 2s ease-in-out infinite;
  margin-bottom: 10px;
}

.slide-text {
  font-size: 1.2rem;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

/* 飘动图片样式 */
.floating-images {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
}

.float-img {
  position: absolute;
  width: 80px;
  height: 80px;
  object-fit: contain;
  opacity: 0.8;
}

/* 每个图片的位置和动画 - 从底部飞入 */
.float-img-1 {
  left: 10%;
  animation: flyUp1 15s linear infinite;
}

.float-img-2 {
  right: 15%;
  animation: flyUp2 18s linear infinite;
}

.float-img-3 {
  left: 20%;
  animation: flyUp3 16s linear infinite;
}

.float-img-4 {
  right: 10%;
  animation: flyUp4 20s linear infinite;
}

.float-img-5 {
  left: 50%;
  transform: translateX(-50%);
  animation: flyUp5 14s linear infinite;
}

.float-img-6 {
  left: 5%;
  animation: flyUp6 19s linear infinite;
}

.float-img-7 {
  right: 5%;
  animation: flyUp7 17s linear infinite;
}

.float-img-8 {
  left: 50%;
  transform: translateX(-50%);
  animation: flyUp8 22s linear infinite;
}

/* 底部图片样式 */
.bottom-image {
  position: absolute;
  bottom: 2rem;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 10;
}

.end-logo {
  width: 100%;
  height: auto;
  object-fit: contain;
}

/* 文字闪动动画 */
@keyframes textBlink {
  0%, 100% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1.05);
  }
}

/* 下滑箭头弹跳动画 */
@keyframes slideArrowBounce {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(10px);
  }
}

/* 3D浮动动画 */
@keyframes float3D {
  0%, 100% {
    transform: translateY(0px) rotateY(0deg) rotateX(0deg) rotateZ(0deg);
  }
  25% {
    transform: translateY(-15px) rotateY(5deg) rotateX(2deg) rotateZ(1deg);
  }
  50% {
    transform: translateY(-25px) rotateY(0deg) rotateX(-2deg) rotateZ(-1deg);
  }
  75% {
    transform: translateY(-15px) rotateY(-5deg) rotateX(2deg) rotateZ(1deg);
  }
}

/* 从底部飞到顶部的动画关键帧 */
@keyframes flyUp1 {
  0% {
    top: 110%;
    opacity: 0;
    transform: rotate(0deg) scale(0.8);
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    top: -10%;
    opacity: 0;
    transform: rotate(360deg) scale(1.2);
  }
}

@keyframes flyUp2 {
  0% {
    top: 110%;
    opacity: 0;
    transform: rotate(0deg) scale(0.8);
  }
  8% {
    opacity: 1;
  }
  92% {
    opacity: 1;
  }
  100% {
    top: -10%;
    opacity: 0;
    transform: rotate(-360deg) scale(1.2);
  }
}

@keyframes flyUp3 {
  0% {
    top: 110%;
    opacity: 0;
    transform: rotate(0deg) scale(0.8);
  }
  12% {
    opacity: 1;
  }
  88% {
    opacity: 1;
  }
  100% {
    top: -10%;
    opacity: 0;
    transform: rotate(180deg) scale(1.2);
  }
}

@keyframes flyUp4 {
  0% {
    top: 110%;
    opacity: 0;
    transform: rotate(0deg) scale(0.8);
  }
  7% {
    opacity: 1;
  }
  93% {
    opacity: 1;
  }
  100% {
    top: -10%;
    opacity: 0;
    transform: rotate(-180deg) scale(1.2);
  }
}

@keyframes flyUp5 {
  0% {
    top: 110%;
    opacity: 0;
    transform: translateX(-50%) rotate(0deg) scale(0.8);
  }
  15% {
    opacity: 1;
  }
  85% {
    opacity: 1;
  }
  100% {
    top: -10%;
    opacity: 0;
    transform: translateX(-50%) rotate(720deg) scale(1.2);
  }
}

@keyframes flyUp6 {
  0% {
    top: 110%;
    opacity: 0;
    transform: rotate(0deg) scale(0.8);
  }
  9% {
    opacity: 1;
  }
  91% {
    opacity: 1;
  }
  100% {
    top: -10%;
    opacity: 0;
    transform: rotate(270deg) scale(1.2);
  }
}

@keyframes flyUp7 {
  0% {
    top: 110%;
    opacity: 0;
    transform: rotate(0deg) scale(0.8);
  }
  11% {
    opacity: 1;
  }
  89% {
    opacity: 1;
  }
  100% {
    top: -10%;
    opacity: 0;
    transform: rotate(-270deg) scale(1.2);
  }
}

@keyframes flyUp8 {
  0% {
    top: 110%;
    opacity: 0;
    transform: translateX(-50%) rotate(0deg) scale(0.8);
  }
  6% {
    opacity: 1;
  }
  94% {
    opacity: 1;
  }
  100% {
    top: -10%;
    opacity: 0;
    transform: translateX(-50%) rotate(-720deg) scale(1.2);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .logo-container {
    top: 1.5rem;
  }

  .clock-image {
    width: 70vw;
    height: 70vw;
    max-width: 600px;
    max-height: 600px;
  }

  .bottom-image {
    bottom: 1.5rem;
  }

  .float-img {
    width: 60px;
    height: 60px;
  }

  .clock-text {
    font-size: 1.5rem;
  }

  .slide-arrow {
    font-size: 2.5rem;
  }

  .slide-text {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .logo-container {
    top: 1rem;
  }

  .clock-image {
    width: 90vw;
    height: 90vw;
    max-width: 500px;
    max-height: 500px;
  }

  .bottom-image {
    bottom: 1rem;
  }

  .float-img {
    width: 50px;
    height: 50px;
  }

  .clock-text {
    font-size: 1.2rem;
  }

  .slide-arrow {
    font-size: 2rem;
  }

  .slide-text {
    font-size: 0.9rem;
  }
}
</style>
