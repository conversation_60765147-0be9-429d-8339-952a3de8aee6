<template>
  <div class="third-container">
    <!-- 顶部导航栏 -->
    <div class="top-nav">
      <div class="nav-left">
        <div class="logo">SLOSH</div>
      </div>
      <div class="nav-center">
        <span class="nav-item">FLAVORS</span>
        <span class="nav-item">CHEERS</span>
      </div>
      <div class="nav-right">
        <div class="menu-icon">
          <div class="menu-line"></div>
          <div class="menu-line"></div>
          <div class="menu-line"></div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- GOOD 文字 -->
      <div class="text-section good-section">
        <div class="big-text good-text">
          <span class="letter" v-for="(letter, index) in 'GOOD'.split('')" :key="index" :style="getLetterStyle(index, 0)">{{ letter }}</span>
        </div>
      </div>

      <!-- TIMES 文字 -->
      <div class="text-section times-section">
        <div class="big-text times-text">
          <span class="letter" v-for="(letter, index) in 'TIMES'.split('')" :key="index" :style="getLetterStyle(index, 1)">{{ letter }}</span>
        </div>
      </div>

      <!-- FLOWING 文字 -->
      <div class="text-section flowing-section">
        <div class="big-text flowing-text">
          <span class="letter" v-for="(letter, index) in 'FLOWING'.split('')" :key="index" :style="getLetterStyle(index, 2)">{{ letter }}</span>
        </div>
      </div>
    </div>

    <!-- 右侧文字 -->
    <div class="side-text right-side">
      <div class="vertical-text">GUARANTEED GOOD TIMES</div>
    </div>

    <!-- 左侧文字 -->
    <div class="side-text left-side">
      <div class="vertical-text">JULY 20 CLS</div>
    </div>

    <!-- 底部心形图标 -->
    <div class="bottom-icon">
      <div class="heart-icon">♥</div>
    </div>

    <!-- 装饰性圆点 -->
    <div class="decorative-dots">
      <div class="dot" v-for="n in 20" :key="n" :style="getDotStyle(n)"></div>
    </div>

    <!-- 下拉提示 -->
    <div class="scroll-hint" :class="{ 'visible': showScrollHint }">
      <div class="scroll-text">Scroll down to play game</div>
      <div class="scroll-arrow">↓</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Third',
  data() {
    return {
      showContent: false,
      showScrollHint: false,
      scrollThreshold: 100, // 滚动阈值
      isScrolling: false
    }
  },
  mounted() {
    // 页面加载后延迟显示内容
    setTimeout(() => {
      this.showContent = true
      this.showScrollHint = true
    }, 300)

    // 添加滚动监听
    window.addEventListener('wheel', this.handleScroll, { passive: false })
    window.addEventListener('touchstart', this.handleTouchStart, { passive: false })
    window.addEventListener('touchmove', this.handleTouchMove, { passive: false })
  },
  beforeDestroy() {
    // 移除事件监听
    window.removeEventListener('wheel', this.handleScroll)
    window.removeEventListener('touchstart', this.handleTouchStart)
    window.removeEventListener('touchmove', this.handleTouchMove)
  },
  methods: {
    getDotStyle(index) {
      const positions = [
        { top: '15%', left: '10%' },
        { top: '25%', left: '85%' },
        { top: '35%', left: '15%' },
        { top: '45%', left: '90%' },
        { top: '55%', left: '8%' },
        { top: '65%', left: '88%' },
        { top: '75%', left: '12%' },
        { top: '85%', left: '85%' },
        { top: '20%', left: '50%' },
        { top: '40%', left: '45%' },
        { top: '60%', left: '55%' },
        { top: '80%', left: '50%' },
        { top: '30%', left: '25%' },
        { top: '50%', left: '75%' },
        { top: '70%', left: '30%' },
        { top: '90%', left: '70%' },
        { top: '10%', left: '60%' },
        { top: '95%', left: '40%' },
        { top: '5%', left: '30%' },
        { top: '95%', left: '20%' }
      ]

      const position = positions[index - 1] || { top: '50%', left: '50%' }
      const delay = (index * 0.2) + 's'

      return {
        top: position.top,
        left: position.left,
        animationDelay: delay
      }
    },

    getLetterStyle(letterIndex, wordIndex) {
      // 创建流水效果的随机偏移和不规则变形
      const baseDelay = wordIndex * 0.4 + letterIndex * 0.15
      const randomOffset = (Math.random() - 0.5) * 30 // -15px 到 15px 的随机偏移
      const randomRotation = (Math.random() - 0.5) * 12 // -6度 到 6度 的随机旋转
      const randomScale = 0.9 + Math.random() * 0.2 // 0.9 到 1.1 的随机缩放

      // 不规则变形参数
      const skewX = (Math.random() - 0.5) * 10 // -5度 到 5度 的X轴倾斜
      const skewY = (Math.random() - 0.5) * 8 // -4度 到 4度 的Y轴倾斜
      const scaleX = 0.95 + Math.random() * 0.1 // X轴缩放 0.95-1.05
      const scaleY = (0.95 + Math.random() * 0.1) * 2.5 // Y轴缩放拉高2.5倍 (2.375-2.625)

      return {
        '--letter-delay': `${baseDelay}s`,
        '--random-offset': `${randomOffset}px`,
        '--random-rotation': `${randomRotation}deg`,
        '--random-scale': randomScale,
        '--skew-x': `${skewX}deg`,
        '--skew-y': `${skewY}deg`,
        '--scale-x': scaleX,
        '--scale-y': scaleY,
        '--wave-phase': letterIndex * 0.3 + wordIndex * 0.8
      }
    },

    // 处理鼠标滚轮事件
    handleScroll(event) {
      if (this.isScrolling) return

      // 检测向下滚动
      if (event.deltaY > this.scrollThreshold) {
        this.navigateToGame()
      }
    },

    // 处理触摸开始事件
    handleTouchStart(event) {
      this.touchStartY = event.touches[0].clientY
    },

    // 处理触摸移动事件
    handleTouchMove(event) {
      if (this.isScrolling) return

      const touchEndY = event.touches[0].clientY
      const deltaY = this.touchStartY - touchEndY

      // 检测向下滑动
      if (deltaY > this.scrollThreshold) {
        this.navigateToGame()
      }
    },

    // 跳转到游戏页面
    navigateToGame() {
      if (this.isScrolling) return

      this.isScrolling = true
      this.showScrollHint = false

      // 添加过渡效果
      setTimeout(() => {
        this.$router.push('/game')
      }, 300)
    }
  }
}
</script>

<style scoped>
.third-container {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #ff1744 0%, #e91e63 50%, #ff1744 100%);
  position: relative;
  overflow: hidden;
  font-family: 'Arial Black', 'Helvetica', sans-serif;
}

/* 顶部导航栏 */
.top-nav {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
  z-index: 100;
  background: rgba(255, 23, 68, 0.9);
  backdrop-filter: blur(10px);
}

.nav-left .logo {
  font-size: 2rem;
  font-weight: 900;
  color: white;
  letter-spacing: 2px;
}

.nav-center {
  display: flex;
  gap: 3rem;
}

.nav-item {
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 1px;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.nav-item:hover {
  opacity: 0.7;
}

.nav-right .menu-icon {
  display: flex;
  flex-direction: column;
  gap: 4px;
  cursor: pointer;
}

.menu-line {
  width: 25px;
  height: 3px;
  background: white;
  border-radius: 2px;
  transition: all 0.3s ease;
}

/* 主要内容区域 */
.main-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 50;
  width: 90%;
  max-width: 800px;
}

.text-section {
  margin: 4rem 0;
}

.big-text {
  font-size: 12rem;
  font-weight: 900;
  color: white;
  letter-spacing: 0.1em;
  line-height: 2.8;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.2rem;
}

.letter {
  display: inline-block;
  text-shadow: 4px 4px 0px rgba(0, 0, 0, 0.2);
  animation: letterFlow 4s ease-in-out infinite;
  animation-delay: var(--letter-delay);
  transform: translateY(var(--random-offset))
             rotate(var(--random-rotation))
             scale(var(--random-scale))
             skew(var(--skew-x), var(--skew-y))
             scaleX(var(--scale-x))
             scaleY(var(--scale-y));
  transition: all 0.3s ease;
  margin: 0 0.05em;
  transform-origin: center center;
}

.letter:hover {
  transform: translateY(calc(var(--random-offset) - 10px))
             rotate(calc(var(--random-rotation) + 5deg))
             scale(calc(var(--random-scale) + 0.1))
             skew(calc(var(--skew-x) + 2deg), calc(var(--skew-y) + 1deg))
             scaleX(calc(var(--scale-x) + 0.05))
             scaleY(calc(var(--scale-y) + 0.075));
}

/* 字母流水动画 */
@keyframes letterFlow {
  0% {
    transform: translateY(var(--random-offset))
               rotate(var(--random-rotation))
               scale(var(--random-scale))
               skew(var(--skew-x), var(--skew-y))
               scaleX(var(--scale-x))
               scaleY(var(--scale-y));
    text-shadow: 4px 4px 0px rgba(0, 0, 0, 0.2);
  }
  25% {
    transform: translateY(calc(var(--random-offset) + 12px))
               rotate(calc(var(--random-rotation) + 3deg))
               scale(calc(var(--random-scale) + 0.05))
               skew(calc(var(--skew-x) + 2deg), calc(var(--skew-y) + 1deg))
               scaleX(calc(var(--scale-x) + 0.03))
               scaleY(calc(var(--scale-y) - 0.03));
    text-shadow: 5px 5px 0px rgba(0, 0, 0, 0.25),
                 0 0 10px rgba(255, 255, 255, 0.3);
  }
  50% {
    transform: translateY(calc(var(--random-offset) - 18px))
               rotate(calc(var(--random-rotation) - 4deg))
               scale(calc(var(--random-scale) + 0.08))
               skew(calc(var(--skew-x) - 3deg), calc(var(--skew-y) + 2deg))
               scaleX(calc(var(--scale-x) - 0.02))
               scaleY(calc(var(--scale-y) + 0.06));
    text-shadow: 6px 6px 0px rgba(0, 0, 0, 0.3),
                 0 0 15px rgba(255, 255, 255, 0.4);
  }
  75% {
    transform: translateY(calc(var(--random-offset) + 8px))
               rotate(calc(var(--random-rotation) + 1.5deg))
               scale(calc(var(--random-scale) + 0.03))
               skew(calc(var(--skew-x) + 1deg), calc(var(--skew-y) - 1deg))
               scaleX(calc(var(--scale-x) + 0.01))
               scaleY(calc(var(--scale-y) + 0.015));
    text-shadow: 4.5px 4.5px 0px rgba(0, 0, 0, 0.22),
                 0 0 12px rgba(255, 255, 255, 0.35);
  }
  100% {
    transform: translateY(var(--random-offset))
               rotate(var(--random-rotation))
               scale(var(--random-scale))
               skew(var(--skew-x), var(--skew-y))
               scaleX(var(--scale-x))
               scaleY(var(--scale-y));
    text-shadow: 4px 4px 0px rgba(0, 0, 0, 0.2);
  }
}

/* 侧边文字 */
.side-text {
  position: absolute;
  z-index: 40;
}

.right-side {
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
}

.left-side {
  left: 2rem;
  bottom: 2rem;
}

.vertical-text {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  font-size: 1rem;
  font-weight: 600;
  color: white;
  letter-spacing: 2px;
  opacity: 0.8;
}

/* 底部心形图标 */
.bottom-icon {
  position: absolute;
  bottom: 3rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 60;
}

.heart-icon {
  font-size: 2.5rem;
  color: white;
  animation: heartBeat 2s ease-in-out infinite;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.heart-icon:hover {
  transform: scale(1.2);
}

@keyframes heartBeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 装饰性圆点 */
.decorative-dots {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}

.dot {
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: dotFloat 4s ease-in-out infinite;
}

@keyframes dotFloat {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) scale(1.2);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .big-text {
    font-size: 8rem;
    gap: 0.15rem;
    line-height: 2.5;
  }

  .text-section {
    margin: 3rem 0;
  }

  .main-content {
    width: 95%;
  }

  .top-nav {
    padding: 0 1rem;
    height: 60px;
  }

  .nav-left .logo {
    font-size: 1.5rem;
  }

  .nav-center {
    gap: 2rem;
  }

  .nav-item {
    font-size: 1rem;
  }

  .side-text {
    display: none;
  }
}

@media (max-width: 480px) {
  .big-text {
    font-size: 5rem;
    gap: 0.1rem;
    letter-spacing: 0.05em;
    line-height: 2.2;
  }

  .text-section {
    margin: 2rem 0;
  }

  .letter {
    margin: 0 0.02em;
  }

  .top-nav {
    padding: 0 1rem;
    height: 50px;
  }

  .nav-left .logo {
    font-size: 1.2rem;
  }

  .nav-center {
    gap: 1.5rem;
  }

  .nav-item {
    font-size: 0.9rem;
  }

  .heart-icon {
    font-size: 2rem;
  }

  .bottom-icon {
    bottom: 2rem;
  }

  .dot {
    width: 6px;
    height: 6px;
  }
}

/* 下拉提示样式 */
.scroll-hint {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 100;
  pointer-events: none;
}

.scroll-hint.visible {
  opacity: 0.8;
}

.scroll-text {
  font-size: 1rem;
  font-weight: 300;
  margin-bottom: 0.5rem;
  letter-spacing: 0.1em;
}

.scroll-arrow {
  font-size: 1.5rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .scroll-hint {
    bottom: 1.5rem;
  }

  .scroll-text {
    font-size: 0.9rem;
  }

  .scroll-arrow {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  .scroll-hint {
    bottom: 1rem;
  }

  .scroll-text {
    font-size: 0.8rem;
  }

  .scroll-arrow {
    font-size: 1.2rem;
  }
}
</style>
