import Vue from 'vue'
import Router from 'vue-router'
import Home from '@/components/Home'
import Second from '@/components/Second'
import Third from '@/components/Third'
import BrickBreaker from '@/components/BrickBreaker'

Vue.use(Router)

export default new Router({
  mode: 'history',
  routes: [
    {
      path: '/',
      name: 'Home',
      component: Home
    },
    {
      path: '/second',
      name: 'Second',
      component: Second
    },
    {
      path: '/third',
      name: 'Third',
      component: Third
    },
    {
      path: '/game',
      name: 'BrickBreaker',
      component: BrickBreaker
    },
    {
      path: '*',
      redirect: '/'
    }
  ],
  scrollBehavior (to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 }
    }
  }
})
