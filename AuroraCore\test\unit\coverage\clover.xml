<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1755363895777" clover="3.2.0">
  <project timestamp="1755363895778" name="All files">
    <metrics statements="278" coveredstatements="0" conditionals="92" coveredconditionals="0" methods="72" coveredmethods="0" elements="442" coveredelements="0" complexity="0" loc="278" ncloc="278" packages="3" files="10" classes="10">
      <package name="src">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <file name="App.vue" path="E:\code\动态时钟网站\AuroraCore\src\App.vue">
          <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
          <line num="12" count="0" type="stmt"/>
          <line num="13" count="0" type="stmt"/>
        </file>
      </package>
      <package name="src.components">
        <metrics statements="264" coveredstatements="0" conditionals="82" coveredconditionals="0" methods="70" coveredmethods="0"/>
        <file name="About.vue" path="E:\code\动态时钟网站\AuroraCore\src\components\About.vue">
          <metrics statements="45" coveredstatements="0" conditionals="15" coveredconditionals="0" methods="17" coveredmethods="0"/>
          <line num="16" count="0" type="stmt"/>
          <line num="17" count="0" type="stmt"/>
          <line num="18" count="0" type="stmt"/>
          <line num="19" count="0" type="stmt"/>
          <line num="20" count="0" type="stmt"/>
          <line num="21" count="0" type="stmt"/>
          <line num="22" count="0" type="stmt"/>
          <line num="23" count="0" type="stmt"/>
          <line num="39" count="0" type="stmt"/>
          <line num="414" count="0" type="cond" truecount="0" falsecount="4"/>
          <line num="415" count="0" type="stmt"/>
          <line num="420" count="0" type="stmt"/>
          <line num="426" count="0" type="stmt"/>
          <line num="437" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="441" count="0" type="cond" truecount="0" falsecount="3"/>
          <line num="448" count="0" type="stmt"/>
          <line num="449" count="0" type="stmt"/>
          <line num="450" count="0" type="stmt"/>
          <line num="457" count="0" type="stmt"/>
          <line num="461" count="0" type="stmt"/>
          <line num="466" count="0" type="stmt"/>
          <line num="473" count="0" type="stmt"/>
          <line num="474" count="0" type="stmt"/>
          <line num="475" count="0" type="stmt"/>
          <line num="480" count="0" type="stmt"/>
          <line num="481" count="0" type="stmt"/>
          <line num="483" count="0" type="stmt"/>
          <line num="488" count="0" type="stmt"/>
          <line num="489" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="490" count="0" type="stmt"/>
          <line num="491" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="497" count="0" type="stmt"/>
          <line num="498" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="499" count="0" type="stmt"/>
          <line num="500" count="0" type="stmt"/>
          <line num="501" count="0" type="stmt"/>
          <line num="507" count="0" type="stmt"/>
          <line num="512" count="0" type="stmt"/>
          <line num="513" count="0" type="stmt"/>
          <line num="514" count="0" type="stmt"/>
          <line num="519" count="0" type="stmt"/>
          <line num="520" count="0" type="stmt"/>
          <line num="521" count="0" type="stmt"/>
          <line num="522" count="0" type="stmt"/>
          <line num="524" count="0" type="stmt"/>
        </file>
        <file name="BrickBreaker.vue" path="E:\code\动态时钟网站\AuroraCore\src\components\BrickBreaker.vue">
          <metrics statements="156" coveredstatements="0" conditionals="50" coveredconditionals="0" methods="19" coveredmethods="0"/>
          <line num="52" count="0" type="stmt"/>
          <line num="103" count="0" type="stmt"/>
          <line num="107" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="108" count="0" type="stmt"/>
          <line num="114" count="0" type="stmt"/>
          <line num="115" count="0" type="stmt"/>
          <line num="116" count="0" type="stmt"/>
          <line num="117" count="0" type="stmt"/>
          <line num="121" count="0" type="stmt"/>
          <line num="122" count="0" type="stmt"/>
          <line num="124" count="0" type="stmt"/>
          <line num="125" count="0" type="stmt"/>
          <line num="126" count="0" type="stmt"/>
          <line num="140" count="0" type="cond" truecount="0" falsecount="4"/>
          <line num="141" count="0" type="stmt"/>
          <line num="142" count="0" type="stmt"/>
          <line num="147" count="0" type="cond" truecount="0" falsecount="4"/>
          <line num="148" count="0" type="stmt"/>
          <line num="149" count="0" type="stmt"/>
          <line num="150" count="0" type="stmt"/>
          <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="156" count="0" type="stmt"/>
          <line num="157" count="0" type="stmt"/>
          <line num="160" count="0" type="cond" truecount="0" falsecount="4"/>
          <line num="161" count="0" type="stmt"/>
          <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="165" count="0" type="stmt"/>
          <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="170" count="0" type="stmt"/>
          <line num="171" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="172" count="0" type="stmt"/>
          <line num="173" count="0" type="stmt"/>
          <line num="175" count="0" type="stmt"/>
          <line num="180" count="0" type="cond" truecount="0" falsecount="5"/>
          <line num="184" count="0" type="stmt"/>
          <line num="185" count="0" type="stmt"/>
          <line num="186" count="0" type="stmt"/>
          <line num="187" count="0" type="stmt"/>
          <line num="188" count="0" type="stmt"/>
          <line num="192" count="0" type="stmt"/>
          <line num="193" count="0" type="cond" truecount="0" falsecount="4"/>
          <line num="194" count="0" type="stmt"/>
          <line num="195" count="0" type="stmt"/>
          <line num="196" count="0" type="stmt"/>
          <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="200" count="0" type="stmt"/>
          <line num="201" count="0" type="stmt"/>
          <line num="203" count="0" type="stmt"/>
          <line num="208" count="0" type="stmt"/>
          <line num="213" count="0" type="stmt"/>
          <line num="216" count="0" type="stmt"/>
          <line num="219" count="0" type="stmt"/>
          <line num="222" count="0" type="stmt"/>
          <line num="225" count="0" type="stmt"/>
          <line num="230" count="0" type="stmt"/>
          <line num="231" count="0" type="stmt"/>
          <line num="234" count="0" type="stmt"/>
          <line num="235" count="0" type="stmt"/>
          <line num="236" count="0" type="stmt"/>
          <line num="237" count="0" type="stmt"/>
          <line num="238" count="0" type="stmt"/>
          <line num="239" count="0" type="stmt"/>
          <line num="240" count="0" type="stmt"/>
          <line num="244" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="245" count="0" type="stmt"/>
          <line num="246" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="247" count="0" type="stmt"/>
          <line num="250" count="0" type="stmt"/>
          <line num="251" count="0" type="stmt"/>
          <line num="252" count="0" type="stmt"/>
          <line num="255" count="0" type="stmt"/>
          <line num="256" count="0" type="stmt"/>
          <line num="259" count="0" type="stmt"/>
          <line num="260" count="0" type="stmt"/>
          <line num="261" count="0" type="stmt"/>
          <line num="262" count="0" type="stmt"/>
          <line num="263" count="0" type="stmt"/>
          <line num="264" count="0" type="stmt"/>
          <line num="265" count="0" type="stmt"/>
          <line num="266" count="0" type="stmt"/>
          <line num="267" count="0" type="stmt"/>
          <line num="270" count="0" type="stmt"/>
          <line num="271" count="0" type="stmt"/>
          <line num="273" count="0" type="stmt"/>
          <line num="279" count="0" type="stmt"/>
          <line num="282" count="0" type="stmt"/>
          <line num="283" count="0" type="stmt"/>
          <line num="284" count="0" type="stmt"/>
          <line num="287" count="0" type="stmt"/>
          <line num="288" count="0" type="stmt"/>
          <line num="291" count="0" type="stmt"/>
          <line num="292" count="0" type="stmt"/>
          <line num="293" count="0" type="stmt"/>
          <line num="294" count="0" type="stmt"/>
          <line num="295" count="0" type="stmt"/>
          <line num="296" count="0" type="stmt"/>
          <line num="297" count="0" type="stmt"/>
          <line num="298" count="0" type="stmt"/>
          <line num="299" count="0" type="stmt"/>
          <line num="302" count="0" type="stmt"/>
          <line num="303" count="0" type="stmt"/>
          <line num="304" count="0" type="stmt"/>
          <line num="305" count="0" type="stmt"/>
          <line num="306" count="0" type="stmt"/>
          <line num="307" count="0" type="stmt"/>
          <line num="308" count="0" type="stmt"/>
          <line num="311" count="0" type="stmt"/>
          <line num="315" count="0" type="stmt"/>
          <line num="318" count="0" type="stmt"/>
          <line num="319" count="0" type="stmt"/>
          <line num="322" count="0" type="stmt"/>
          <line num="323" count="0" type="stmt"/>
          <line num="326" count="0" type="stmt"/>
          <line num="327" count="0" type="stmt"/>
          <line num="328" count="0" type="stmt"/>
          <line num="333" count="0" type="stmt"/>
          <line num="334" count="0" type="stmt"/>
          <line num="335" count="0" type="stmt"/>
          <line num="336" count="0" type="stmt"/>
          <line num="337" count="0" type="stmt"/>
          <line num="338" count="0" type="stmt"/>
          <line num="341" count="0" type="stmt"/>
          <line num="342" count="0" type="stmt"/>
          <line num="345" count="0" type="stmt"/>
          <line num="346" count="0" type="stmt"/>
          <line num="347" count="0" type="stmt"/>
          <line num="350" count="0" type="stmt"/>
          <line num="351" count="0" type="stmt"/>
          <line num="352" count="0" type="stmt"/>
          <line num="353" count="0" type="stmt"/>
          <line num="355" count="0" type="stmt"/>
          <line num="359" count="0" type="cond" truecount="0" falsecount="4"/>
          <line num="366" count="0" type="cond" truecount="0" falsecount="5"/>
          <line num="367" count="0" type="stmt"/>
          <line num="368" count="0" type="stmt"/>
          <line num="369" count="0" type="stmt"/>
          <line num="374" count="0" type="stmt"/>
          <line num="375" count="0" type="stmt"/>
          <line num="376" count="0" type="stmt"/>
          <line num="377" count="0" type="stmt"/>
          <line num="381" count="0" type="stmt"/>
          <line num="382" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="383" count="0" type="stmt"/>
          <line num="388" count="0" type="stmt"/>
          <line num="389" count="0" type="stmt"/>
          <line num="390" count="0" type="stmt"/>
          <line num="391" count="0" type="stmt"/>
          <line num="392" count="0" type="stmt"/>
          <line num="393" count="0" type="stmt"/>
          <line num="394" count="0" type="stmt"/>
          <line num="396" count="0" type="stmt"/>
          <line num="397" count="0" type="stmt"/>
          <line num="398" count="0" type="stmt"/>
          <line num="399" count="0" type="stmt"/>
          <line num="401" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="402" count="0" type="stmt"/>
        </file>
        <file name="Contact.vue" path="E:\code\动态时钟网站\AuroraCore\src\components\Contact.vue">
          <metrics statements="7" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="2" coveredmethods="0"/>
          <line num="174" count="0" type="stmt"/>
          <line num="248" count="0" type="cond" truecount="0" falsecount="4"/>
          <line num="249" count="0" type="stmt"/>
          <line num="252" count="0" type="stmt"/>
          <line num="254" count="0" type="stmt"/>
          <line num="255" count="0" type="stmt"/>
          <line num="258" count="0" type="stmt"/>
        </file>
        <file name="Footer.vue" path="E:\code\动态时钟网站\AuroraCore\src\components\Footer.vue">
          <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
          <line num="185" count="0" type="stmt"/>
        </file>
        <file name="Header.vue" path="E:\code\动态时钟网站\AuroraCore\src\components\Header.vue">
          <metrics statements="10" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="6" coveredmethods="0"/>
          <line num="67" count="0" type="stmt"/>
          <line num="73" count="0" type="stmt"/>
          <line num="74" count="0" type="stmt"/>
          <line num="77" count="0" type="stmt"/>
          <line num="81" count="0" type="stmt"/>
          <line num="84" count="0" type="stmt"/>
          <line num="87" count="0" type="stmt"/>
          <line num="88" count="0" type="stmt"/>
          <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="90" count="0" type="stmt"/>
        </file>
        <file name="Home.vue" path="E:\code\动态时钟网站\AuroraCore\src\components\Home.vue">
          <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
          <line num="147" count="0" type="stmt"/>
        </file>
        <file name="News.vue" path="E:\code\动态时钟网站\AuroraCore\src\components\News.vue">
          <metrics statements="37" coveredstatements="0" conditionals="9" coveredconditionals="0" methods="20" coveredmethods="0"/>
          <line num="207" count="0" type="stmt"/>
          <line num="340" count="0" type="stmt"/>
          <line num="342" count="0" type="stmt"/>
          <line num="343" count="0" type="stmt"/>
          <line num="344" count="0" type="stmt"/>
          <line num="347" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="348" count="0" type="stmt"/>
          <line num="349" count="0" type="stmt"/>
          <line num="353" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="354" count="0" type="stmt"/>
          <line num="355" count="0" type="cond" truecount="0" falsecount="3"/>
          <line num="360" count="0" type="stmt"/>
          <line num="363" count="0" type="stmt"/>
          <line num="364" count="0" type="stmt"/>
          <line num="367" count="0" type="stmt"/>
          <line num="368" count="0" type="stmt"/>
          <line num="369" count="0" type="stmt"/>
          <line num="372" count="0" type="stmt"/>
          <line num="377" count="0" type="stmt"/>
          <line num="380" count="0" type="stmt"/>
          <line num="385" count="0" type="stmt"/>
          <line num="386" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="389" count="0" type="stmt"/>
          <line num="390" count="0" type="stmt"/>
          <line num="397" count="0" type="stmt"/>
          <line num="398" count="0" type="stmt"/>
          <line num="404" count="0" type="stmt"/>
          <line num="405" count="0" type="stmt"/>
          <line num="408" count="0" type="stmt"/>
          <line num="410" count="0" type="stmt"/>
          <line num="413" count="0" type="stmt"/>
          <line num="416" count="0" type="stmt"/>
          <line num="418" count="0" type="stmt"/>
          <line num="419" count="0" type="stmt"/>
          <line num="420" count="0" type="stmt"/>
          <line num="421" count="0" type="stmt"/>
          <line num="426" count="0" type="stmt"/>
        </file>
        <file name="Products.vue" path="E:\code\动态时钟网站\AuroraCore\src\components\Products.vue">
          <metrics statements="7" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="4" coveredmethods="0"/>
          <line num="201" count="0" type="stmt"/>
          <line num="412" count="0" type="stmt"/>
          <line num="413" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="414" count="0" type="stmt"/>
          <line num="416" count="0" type="stmt"/>
          <line num="421" count="0" type="stmt"/>
          <line num="422" count="0" type="stmt"/>
        </file>
      </package>
      <package name="src.components.sections">
        <metrics statements="12" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <file name="PageHero.vue" path="E:\code\动态时钟网站\AuroraCore\src\components\sections\PageHero.vue">
          <metrics statements="12" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="2" coveredmethods="0"/>
          <line num="35" count="0" type="stmt"/>
          <line num="36" count="0" type="stmt"/>
          <line num="39" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="41" count="0" type="stmt"/>
          <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="43" count="0" type="stmt"/>
          <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="46" count="0" type="cond" truecount="0" falsecount="2"/>
          <line num="47" count="0" type="stmt"/>
          <line num="50" count="0" type="stmt"/>
          <line num="54" count="0" type="stmt"/>
        </file>
      </package>
    </metrics>
  </project>
</coverage>
