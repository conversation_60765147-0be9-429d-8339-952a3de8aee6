{"E:\\code\\动态时钟网站\\AuroraCore\\src\\App.vue": {"path": "E:\\code\\动态时钟网站\\AuroraCore\\src\\App.vue", "statementMap": {"0": {"start": {"line": 12, "column": 0}, "end": {"line": null, "column": -1}}, "1": {"start": {"line": 13, "column": 0}, "end": {"line": null, "column": -1}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {}, "b": {}}, "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\About.vue": {"path": "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\About.vue", "statementMap": {"0": {"start": {"line": 16, "column": 0}, "end": {"line": null, "column": -1}}, "1": {"start": {"line": 17, "column": 0}, "end": {"line": null, "column": -1}}, "2": {"start": {"line": 18, "column": 0}, "end": {"line": null, "column": -1}}, "3": {"start": {"line": 19, "column": 0}, "end": {"line": null, "column": -1}}, "4": {"start": {"line": 20, "column": 0}, "end": {"line": null, "column": -1}}, "5": {"start": {"line": 21, "column": 0}, "end": {"line": null, "column": -1}}, "6": {"start": {"line": 22, "column": 0}, "end": {"line": null, "column": -1}}, "7": {"start": {"line": 23, "column": 0}, "end": {"line": null, "column": -1}}, "8": {"start": {"line": 39, "column": 0}, "end": {"line": null, "column": -1}}, "9": {"start": {"line": 414, "column": 0}, "end": {"line": null, "column": -1}}, "10": {"start": {"line": 415, "column": 0}, "end": {"line": null, "column": -1}}, "11": {"start": {"line": 420, "column": 0}, "end": {"line": null, "column": -1}}, "12": {"start": {"line": 426, "column": 0}, "end": {"line": null, "column": -1}}, "13": {"start": {"line": 437, "column": 0}, "end": {"line": null, "column": -1}}, "14": {"start": {"line": 441, "column": 0}, "end": {"line": null, "column": -1}}, "15": {"start": {"line": 441, "column": 0}, "end": {"line": 452, "column": 0}}, "16": {"start": {"line": 448, "column": 0}, "end": {"line": 451, "column": 0}}, "17": {"start": {"line": 449, "column": 0}, "end": {"line": null, "column": -1}}, "18": {"start": {"line": 450, "column": 0}, "end": {"line": null, "column": -1}}, "19": {"start": {"line": 457, "column": 0}, "end": {"line": null, "column": -1}}, "20": {"start": {"line": 461, "column": 0}, "end": {"line": null, "column": -1}}, "21": {"start": {"line": 466, "column": 0}, "end": {"line": null, "column": -1}}, "22": {"start": {"line": 473, "column": 0}, "end": {"line": null, "column": -1}}, "23": {"start": {"line": 474, "column": 0}, "end": {"line": null, "column": -1}}, "24": {"start": {"line": 475, "column": 0}, "end": {"line": null, "column": -1}}, "25": {"start": {"line": 480, "column": 0}, "end": {"line": 481, "column": 0}}, "26": {"start": {"line": 481, "column": 0}, "end": {"line": null, "column": -1}}, "27": {"start": {"line": 483, "column": 0}, "end": {"line": null, "column": -1}}, "28": {"start": {"line": 488, "column": 0}, "end": {"line": null, "column": -1}}, "29": {"start": {"line": 489, "column": 0}, "end": {"line": 492, "column": 0}}, "30": {"start": {"line": 490, "column": 0}, "end": {"line": null, "column": -1}}, "31": {"start": {"line": 491, "column": 0}, "end": {"line": null, "column": -1}}, "32": {"start": {"line": 497, "column": 0}, "end": {"line": null, "column": -1}}, "33": {"start": {"line": 498, "column": 0}, "end": {"line": 502, "column": 0}}, "34": {"start": {"line": 499, "column": 0}, "end": {"line": null, "column": -1}}, "35": {"start": {"line": 500, "column": 0}, "end": {"line": null, "column": -1}}, "36": {"start": {"line": 501, "column": 0}, "end": {"line": null, "column": -1}}, "37": {"start": {"line": 507, "column": 0}, "end": {"line": null, "column": -1}}, "38": {"start": {"line": 512, "column": 0}, "end": {"line": null, "column": -1}}, "39": {"start": {"line": 513, "column": 0}, "end": {"line": null, "column": -1}}, "40": {"start": {"line": 514, "column": 0}, "end": {"line": null, "column": -1}}, "41": {"start": {"line": 519, "column": 0}, "end": {"line": 525, "column": 0}}, "42": {"start": {"line": 520, "column": 0}, "end": {"line": null, "column": -1}}, "43": {"start": {"line": 521, "column": 0}, "end": {"line": null, "column": -1}}, "44": {"start": {"line": 522, "column": 0}, "end": {"line": null, "column": -1}}, "45": {"start": {"line": 524, "column": 0}, "end": {"line": null, "column": -1}}}, "fnMap": {"0": {"name": "data", "decl": {"start": {"line": 38, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 38, "column": 0}, "end": {"line": 411, "column": 0}}}, "1": {"name": "created", "decl": {"start": {"line": 414, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 414, "column": 0}, "end": {"line": 421, "column": 0}}}, "2": {"name": "getSectionComponent", "decl": {"start": {"line": 425, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 425, "column": 0}, "end": {"line": 438, "column": 0}}}, "3": {"name": "loadPageConfig", "decl": {"start": {"line": 441, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 441, "column": 0}, "end": {"line": 453, "column": 0}}}, "4": {"name": "(anonymous_11)", "decl": {"start": {"line": 448, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 448, "column": 0}, "end": {"line": 451, "column": 0}}}, "5": {"name": "updatePageConfig", "decl": {"start": {"line": 456, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 456, "column": 0}, "end": {"line": 462, "column": 0}}}, "6": {"name": "addSection", "decl": {"start": {"line": 465, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 465, "column": 0}, "end": {"line": 476, "column": 0}}}, "7": {"name": "removeSection", "decl": {"start": {"line": 479, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 479, "column": 0}, "end": {"line": 484, "column": 0}}}, "8": {"name": "(anonymous_15)", "decl": {"start": {"line": 480, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 480, "column": 0}, "end": {"line": 481, "column": 0}}}, "9": {"name": "toggleSection", "decl": {"start": {"line": 487, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 487, "column": 0}, "end": {"line": 493, "column": 0}}}, "10": {"name": "(anonymous_17)", "decl": {"start": {"line": 488, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 488, "column": 0}, "end": {"line": null, "column": -1}}}, "11": {"name": "reorderSection", "decl": {"start": {"line": 496, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 496, "column": 0}, "end": {"line": 503, "column": 0}}}, "12": {"name": "(anonymous_19)", "decl": {"start": {"line": 497, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 497, "column": 0}, "end": {"line": null, "column": -1}}}, "13": {"name": "sortSections", "decl": {"start": {"line": 506, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 506, "column": 0}, "end": {"line": 508, "column": 0}}}, "14": {"name": "(anonymous_21)", "decl": {"start": {"line": 507, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 507, "column": 0}, "end": {"line": null, "column": -1}}}, "15": {"name": "exportConfig", "decl": {"start": {"line": 511, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 511, "column": 0}, "end": {"line": 515, "column": 0}}}, "16": {"name": "importConfig", "decl": {"start": {"line": 518, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 518, "column": 0}, "end": {"line": 526, "column": 0}}}}, "branchMap": {"0": {"loc": {"start": {"line": 414, "column": 0}, "end": {"line": null, "column": -1}}, "type": "switch", "locations": [{"start": {"line": 414, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 414, "column": 0}, "end": {"line": 420, "column": 0}}, {"start": {"line": 414, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 414, "column": 0}, "end": {"line": null, "column": -1}}]}, "1": {"loc": {"start": {"line": 437, "column": 0}, "end": {"line": null, "column": -1}}, "type": "binary-expr", "locations": [{"start": {"line": 437, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 437, "column": 0}, "end": {"line": null, "column": -1}}]}, "2": {"loc": {"start": {"line": 441, "column": 0}, "end": {"line": 452, "column": 0}}, "type": "switch", "locations": [{"start": {"line": 441, "column": 0}, "end": {"line": 452, "column": 0}}, {"start": {"line": 441, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 441, "column": 0}, "end": {"line": null, "column": -1}}]}, "3": {"loc": {"start": {"line": 489, "column": 0}, "end": {"line": 492, "column": 0}}, "type": "if", "locations": [{"start": {"line": 489, "column": 0}, "end": {"line": 492, "column": 0}}, {"start": {"line": 489, "column": 0}, "end": {"line": 492, "column": 0}}]}, "4": {"loc": {"start": {"line": 491, "column": 0}, "end": {"line": null, "column": -1}}, "type": "cond-expr", "locations": [{"start": {"line": 491, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 491, "column": 0}, "end": {"line": null, "column": -1}}]}, "5": {"loc": {"start": {"line": 498, "column": 0}, "end": {"line": 502, "column": 0}}, "type": "if", "locations": [{"start": {"line": 498, "column": 0}, "end": {"line": 502, "column": 0}}, {"start": {"line": 498, "column": 0}, "end": {"line": 502, "column": 0}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "b": {"0": [0, 0, 0, 0], "1": [0, 0], "2": [0, 0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0]}}, "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\BrickBreaker.vue": {"path": "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\BrickBreaker.vue", "statementMap": {"0": {"start": {"line": 52, "column": 0}, "end": {"line": null, "column": -1}}, "1": {"start": {"line": 103, "column": 0}, "end": {"line": null, "column": -1}}, "2": {"start": {"line": 107, "column": 0}, "end": {"line": 109, "column": 0}}, "3": {"start": {"line": 108, "column": 0}, "end": {"line": null, "column": -1}}, "4": {"start": {"line": 114, "column": 0}, "end": {"line": null, "column": -1}}, "5": {"start": {"line": 115, "column": 0}, "end": {"line": null, "column": -1}}, "6": {"start": {"line": 116, "column": 0}, "end": {"line": null, "column": -1}}, "7": {"start": {"line": 117, "column": 0}, "end": {"line": null, "column": -1}}, "8": {"start": {"line": 121, "column": 0}, "end": {"line": null, "column": -1}}, "9": {"start": {"line": 122, "column": 0}, "end": {"line": null, "column": -1}}, "10": {"start": {"line": 124, "column": 0}, "end": {"line": 136, "column": 0}}, "11": {"start": {"line": 125, "column": 0}, "end": {"line": 135, "column": 0}}, "12": {"start": {"line": 126, "column": 0}, "end": {"line": null, "column": -1}}, "13": {"start": {"line": 140, "column": 0}, "end": {"line": 143, "column": 0}}, "14": {"start": {"line": 141, "column": 0}, "end": {"line": null, "column": -1}}, "15": {"start": {"line": 142, "column": 0}, "end": {"line": null, "column": -1}}, "16": {"start": {"line": 147, "column": 0}, "end": {"line": 151, "column": 0}}, "17": {"start": {"line": 148, "column": 0}, "end": {"line": null, "column": -1}}, "18": {"start": {"line": 149, "column": 0}, "end": {"line": null, "column": -1}}, "19": {"start": {"line": 150, "column": 0}, "end": {"line": null, "column": -1}}, "20": {"start": {"line": 156, "column": 0}, "end": {"line": null, "column": -1}}, "21": {"start": {"line": 157, "column": 0}, "end": {"line": null, "column": -1}}, "22": {"start": {"line": 160, "column": 0}, "end": {"line": 162, "column": 0}}, "23": {"start": {"line": 161, "column": 0}, "end": {"line": null, "column": -1}}, "24": {"start": {"line": 164, "column": 0}, "end": {"line": 166, "column": 0}}, "25": {"start": {"line": 165, "column": 0}, "end": {"line": null, "column": -1}}, "26": {"start": {"line": 169, "column": 0}, "end": {"line": 177, "column": 0}}, "27": {"start": {"line": 170, "column": 0}, "end": {"line": null, "column": -1}}, "28": {"start": {"line": 171, "column": 0}, "end": {"line": 176, "column": 0}}, "29": {"start": {"line": 172, "column": 0}, "end": {"line": null, "column": -1}}, "30": {"start": {"line": 173, "column": 0}, "end": {"line": null, "column": -1}}, "31": {"start": {"line": 175, "column": 0}, "end": {"line": null, "column": -1}}, "32": {"start": {"line": 180, "column": 0}, "end": {"line": 189, "column": 0}}, "33": {"start": {"line": 184, "column": 0}, "end": {"line": null, "column": -1}}, "34": {"start": {"line": 185, "column": 0}, "end": {"line": null, "column": -1}}, "35": {"start": {"line": 186, "column": 0}, "end": {"line": null, "column": -1}}, "36": {"start": {"line": 187, "column": 0}, "end": {"line": null, "column": -1}}, "37": {"start": {"line": 188, "column": 0}, "end": {"line": null, "column": -1}}, "38": {"start": {"line": 154, "column": 0}, "end": {"line": null, "column": -1}}, "39": {"start": {"line": 192, "column": 0}, "end": {"line": 205, "column": 0}}, "40": {"start": {"line": 192, "column": 0}, "end": {"line": null, "column": -1}}, "41": {"start": {"line": 193, "column": 0}, "end": {"line": 204, "column": 0}}, "42": {"start": {"line": 194, "column": 0}, "end": {"line": null, "column": -1}}, "43": {"start": {"line": 195, "column": 0}, "end": {"line": null, "column": -1}}, "44": {"start": {"line": 196, "column": 0}, "end": {"line": null, "column": -1}}, "45": {"start": {"line": 199, "column": 0}, "end": {"line": 202, "column": 0}}, "46": {"start": {"line": 199, "column": 0}, "end": {"line": null, "column": -1}}, "47": {"start": {"line": 200, "column": 0}, "end": {"line": null, "column": -1}}, "48": {"start": {"line": 201, "column": 0}, "end": {"line": null, "column": -1}}, "49": {"start": {"line": 203, "column": 0}, "end": {"line": null, "column": -1}}, "50": {"start": {"line": 208, "column": 0}, "end": {"line": null, "column": -1}}, "51": {"start": {"line": 213, "column": 0}, "end": {"line": null, "column": -1}}, "52": {"start": {"line": 216, "column": 0}, "end": {"line": null, "column": -1}}, "53": {"start": {"line": 219, "column": 0}, "end": {"line": null, "column": -1}}, "54": {"start": {"line": 222, "column": 0}, "end": {"line": null, "column": -1}}, "55": {"start": {"line": 225, "column": 0}, "end": {"line": null, "column": -1}}, "56": {"start": {"line": 230, "column": 0}, "end": {"line": null, "column": -1}}, "57": {"start": {"line": 231, "column": 0}, "end": {"line": null, "column": -1}}, "58": {"start": {"line": 234, "column": 0}, "end": {"line": null, "column": -1}}, "59": {"start": {"line": 235, "column": 0}, "end": {"line": null, "column": -1}}, "60": {"start": {"line": 236, "column": 0}, "end": {"line": 241, "column": 0}}, "61": {"start": {"line": 237, "column": 0}, "end": {"line": null, "column": -1}}, "62": {"start": {"line": 238, "column": 0}, "end": {"line": null, "column": -1}}, "63": {"start": {"line": 239, "column": 0}, "end": {"line": null, "column": -1}}, "64": {"start": {"line": 240, "column": 0}, "end": {"line": null, "column": -1}}, "65": {"start": {"line": 244, "column": 0}, "end": {"line": null, "column": -1}}, "66": {"start": {"line": 245, "column": 0}, "end": {"line": 275, "column": 0}}, "67": {"start": {"line": 245, "column": 0}, "end": {"line": null, "column": -1}}, "68": {"start": {"line": 246, "column": 0}, "end": {"line": 274, "column": 0}}, "69": {"start": {"line": 247, "column": 0}, "end": {"line": null, "column": -1}}, "70": {"start": {"line": 250, "column": 0}, "end": {"line": null, "column": -1}}, "71": {"start": {"line": 251, "column": 0}, "end": {"line": null, "column": -1}}, "72": {"start": {"line": 252, "column": 0}, "end": {"line": null, "column": -1}}, "73": {"start": {"line": 255, "column": 0}, "end": {"line": null, "column": -1}}, "74": {"start": {"line": 256, "column": 0}, "end": {"line": null, "column": -1}}, "75": {"start": {"line": 259, "column": 0}, "end": {"line": null, "column": -1}}, "76": {"start": {"line": 260, "column": 0}, "end": {"line": null, "column": -1}}, "77": {"start": {"line": 261, "column": 0}, "end": {"line": null, "column": -1}}, "78": {"start": {"line": 262, "column": 0}, "end": {"line": null, "column": -1}}, "79": {"start": {"line": 263, "column": 0}, "end": {"line": null, "column": -1}}, "80": {"start": {"line": 264, "column": 0}, "end": {"line": null, "column": -1}}, "81": {"start": {"line": 265, "column": 0}, "end": {"line": null, "column": -1}}, "82": {"start": {"line": 266, "column": 0}, "end": {"line": null, "column": -1}}, "83": {"start": {"line": 267, "column": 0}, "end": {"line": null, "column": -1}}, "84": {"start": {"line": 270, "column": 0}, "end": {"line": null, "column": -1}}, "85": {"start": {"line": 271, "column": 0}, "end": {"line": null, "column": -1}}, "86": {"start": {"line": 273, "column": 0}, "end": {"line": null, "column": -1}}, "87": {"start": {"line": 279, "column": 0}, "end": {"line": null, "column": -1}}, "88": {"start": {"line": 282, "column": 0}, "end": {"line": null, "column": -1}}, "89": {"start": {"line": 283, "column": 0}, "end": {"line": null, "column": -1}}, "90": {"start": {"line": 284, "column": 0}, "end": {"line": null, "column": -1}}, "91": {"start": {"line": 287, "column": 0}, "end": {"line": null, "column": -1}}, "92": {"start": {"line": 288, "column": 0}, "end": {"line": null, "column": -1}}, "93": {"start": {"line": 291, "column": 0}, "end": {"line": null, "column": -1}}, "94": {"start": {"line": 292, "column": 0}, "end": {"line": null, "column": -1}}, "95": {"start": {"line": 293, "column": 0}, "end": {"line": null, "column": -1}}, "96": {"start": {"line": 294, "column": 0}, "end": {"line": null, "column": -1}}, "97": {"start": {"line": 295, "column": 0}, "end": {"line": null, "column": -1}}, "98": {"start": {"line": 296, "column": 0}, "end": {"line": null, "column": -1}}, "99": {"start": {"line": 297, "column": 0}, "end": {"line": null, "column": -1}}, "100": {"start": {"line": 298, "column": 0}, "end": {"line": null, "column": -1}}, "101": {"start": {"line": 299, "column": 0}, "end": {"line": null, "column": -1}}, "102": {"start": {"line": 302, "column": 0}, "end": {"line": null, "column": -1}}, "103": {"start": {"line": 303, "column": 0}, "end": {"line": null, "column": -1}}, "104": {"start": {"line": 304, "column": 0}, "end": {"line": 309, "column": 0}}, "105": {"start": {"line": 305, "column": 0}, "end": {"line": null, "column": -1}}, "106": {"start": {"line": 306, "column": 0}, "end": {"line": null, "column": -1}}, "107": {"start": {"line": 307, "column": 0}, "end": {"line": null, "column": -1}}, "108": {"start": {"line": 308, "column": 0}, "end": {"line": null, "column": -1}}, "109": {"start": {"line": 311, "column": 0}, "end": {"line": null, "column": -1}}, "110": {"start": {"line": 315, "column": 0}, "end": {"line": null, "column": -1}}, "111": {"start": {"line": 318, "column": 0}, "end": {"line": null, "column": -1}}, "112": {"start": {"line": 319, "column": 0}, "end": {"line": null, "column": -1}}, "113": {"start": {"line": 322, "column": 0}, "end": {"line": null, "column": -1}}, "114": {"start": {"line": 323, "column": 0}, "end": {"line": null, "column": -1}}, "115": {"start": {"line": 326, "column": 0}, "end": {"line": null, "column": -1}}, "116": {"start": {"line": 327, "column": 0}, "end": {"line": null, "column": -1}}, "117": {"start": {"line": 328, "column": 0}, "end": {"line": null, "column": -1}}, "118": {"start": {"line": 333, "column": 0}, "end": {"line": 339, "column": 0}}, "119": {"start": {"line": 334, "column": 0}, "end": {"line": null, "column": -1}}, "120": {"start": {"line": 335, "column": 0}, "end": {"line": null, "column": -1}}, "121": {"start": {"line": 336, "column": 0}, "end": {"line": null, "column": -1}}, "122": {"start": {"line": 337, "column": 0}, "end": {"line": null, "column": -1}}, "123": {"start": {"line": 338, "column": 0}, "end": {"line": null, "column": -1}}, "124": {"start": {"line": 341, "column": 0}, "end": {"line": null, "column": -1}}, "125": {"start": {"line": 342, "column": 0}, "end": {"line": null, "column": -1}}, "126": {"start": {"line": 345, "column": 0}, "end": {"line": null, "column": -1}}, "127": {"start": {"line": 346, "column": 0}, "end": {"line": null, "column": -1}}, "128": {"start": {"line": 347, "column": 0}, "end": {"line": null, "column": -1}}, "129": {"start": {"line": 350, "column": 0}, "end": {"line": null, "column": -1}}, "130": {"start": {"line": 351, "column": 0}, "end": {"line": null, "column": -1}}, "131": {"start": {"line": 352, "column": 0}, "end": {"line": null, "column": -1}}, "132": {"start": {"line": 353, "column": 0}, "end": {"line": null, "column": -1}}, "133": {"start": {"line": 355, "column": 0}, "end": {"line": null, "column": -1}}, "134": {"start": {"line": 359, "column": 0}, "end": {"line": null, "column": -1}}, "135": {"start": {"line": 366, "column": 0}, "end": {"line": 370, "column": 0}}, "136": {"start": {"line": 367, "column": 0}, "end": {"line": null, "column": -1}}, "137": {"start": {"line": 368, "column": 0}, "end": {"line": null, "column": -1}}, "138": {"start": {"line": 369, "column": 0}, "end": {"line": null, "column": -1}}, "139": {"start": {"line": 374, "column": 0}, "end": {"line": null, "column": -1}}, "140": {"start": {"line": 375, "column": 0}, "end": {"line": null, "column": -1}}, "141": {"start": {"line": 376, "column": 0}, "end": {"line": null, "column": -1}}, "142": {"start": {"line": 377, "column": 0}, "end": {"line": null, "column": -1}}, "143": {"start": {"line": 381, "column": 0}, "end": {"line": null, "column": -1}}, "144": {"start": {"line": 382, "column": 0}, "end": {"line": 384, "column": 0}}, "145": {"start": {"line": 383, "column": 0}, "end": {"line": null, "column": -1}}, "146": {"start": {"line": 388, "column": 0}, "end": {"line": null, "column": -1}}, "147": {"start": {"line": 389, "column": 0}, "end": {"line": null, "column": -1}}, "148": {"start": {"line": 390, "column": 0}, "end": {"line": null, "column": -1}}, "149": {"start": {"line": 391, "column": 0}, "end": {"line": null, "column": -1}}, "150": {"start": {"line": 392, "column": 0}, "end": {"line": null, "column": -1}}, "151": {"start": {"line": 393, "column": 0}, "end": {"line": null, "column": -1}}, "152": {"start": {"line": 394, "column": 0}, "end": {"line": null, "column": -1}}, "153": {"start": {"line": 396, "column": 0}, "end": {"line": null, "column": -1}}, "154": {"start": {"line": 397, "column": 0}, "end": {"line": null, "column": -1}}, "155": {"start": {"line": 398, "column": 0}, "end": {"line": null, "column": -1}}, "156": {"start": {"line": 399, "column": 0}, "end": {"line": null, "column": -1}}, "157": {"start": {"line": 401, "column": 0}, "end": {"line": 403, "column": 0}}, "158": {"start": {"line": 402, "column": 0}, "end": {"line": null, "column": -1}}}, "fnMap": {"0": {"name": "data", "decl": {"start": {"line": 51, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 51, "column": 0}, "end": {"line": 100, "column": 0}}}, "1": {"name": "mounted", "decl": {"start": {"line": 102, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 102, "column": 0}, "end": {"line": 104, "column": 0}}}, "2": {"name": "beforeUnmount", "decl": {"start": {"line": 106, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 106, "column": 0}, "end": {"line": 110, "column": 0}}}, "3": {"name": "initGame", "decl": {"start": {"line": 113, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 113, "column": 0}, "end": {"line": 118, "column": 0}}}, "4": {"name": "createBricks", "decl": {"start": {"line": 120, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 120, "column": 0}, "end": {"line": 137, "column": 0}}}, "5": {"name": "startGame", "decl": {"start": {"line": 139, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 139, "column": 0}, "end": {"line": 144, "column": 0}}}, "6": {"name": "gameLoop", "decl": {"start": {"line": 146, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 146, "column": 0}, "end": {"line": 152, "column": 0}}}, "7": {"name": "update", "decl": {"start": {"line": 154, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 154, "column": 0}, "end": {"line": 209, "column": 0}}}, "8": {"name": "(anonymous_10)", "decl": {"start": {"line": 199, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 199, "column": 0}, "end": {"line": null, "column": -1}}}, "9": {"name": "draw", "decl": {"start": {"line": 211, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 211, "column": 0}, "end": {"line": 226, "column": 0}}}, "10": {"name": "drawBackground", "decl": {"start": {"line": 228, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 228, "column": 0}, "end": {"line": 242, "column": 0}}}, "11": {"name": "drawBricks", "decl": {"start": {"line": 244, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 244, "column": 0}, "end": {"line": 276, "column": 0}}}, "12": {"name": "drawPaddle", "decl": {"start": {"line": 278, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 278, "column": 0}, "end": {"line": 312, "column": 0}}}, "13": {"name": "drawBall", "decl": {"start": {"line": 314, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 314, "column": 0}, "end": {"line": 356, "column": 0}}}, "14": {"name": "ballBrickCollision", "decl": {"start": {"line": 358, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 358, "column": 0}, "end": {"line": 363, "column": 0}}}, "15": {"name": "movePaddle", "decl": {"start": {"line": 365, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 365, "column": 0}, "end": {"line": 371, "column": 0}}}, "16": {"name": "resetBall", "decl": {"start": {"line": 373, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 373, "column": 0}, "end": {"line": 378, "column": 0}}}, "17": {"name": "pauseGame", "decl": {"start": {"line": 380, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 380, "column": 0}, "end": {"line": 385, "column": 0}}}, "18": {"name": "resetGame", "decl": {"start": {"line": 387, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 387, "column": 0}, "end": {"line": 404, "column": 0}}}}, "branchMap": {"0": {"loc": {"start": {"line": 107, "column": 0}, "end": {"line": 109, "column": 0}}, "type": "if", "locations": [{"start": {"line": 107, "column": 0}, "end": {"line": 109, "column": 0}}, {"start": {"line": 107, "column": 0}, "end": {"line": 109, "column": 0}}]}, "1": {"loc": {"start": {"line": 140, "column": 0}, "end": {"line": 143, "column": 0}}, "type": "if", "locations": [{"start": {"line": 140, "column": 0}, "end": {"line": 143, "column": 0}}, {"start": {"line": 140, "column": 0}, "end": {"line": 143, "column": 0}}]}, "2": {"loc": {"start": {"line": 140, "column": 0}, "end": {"line": null, "column": -1}}, "type": "binary-expr", "locations": [{"start": {"line": 140, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 140, "column": 0}, "end": {"line": null, "column": -1}}]}, "3": {"loc": {"start": {"line": 147, "column": 0}, "end": {"line": 151, "column": 0}}, "type": "if", "locations": [{"start": {"line": 147, "column": 0}, "end": {"line": 151, "column": 0}}, {"start": {"line": 147, "column": 0}, "end": {"line": 151, "column": 0}}]}, "4": {"loc": {"start": {"line": 147, "column": 0}, "end": {"line": null, "column": -1}}, "type": "binary-expr", "locations": [{"start": {"line": 147, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 147, "column": 0}, "end": {"line": null, "column": -1}}]}, "5": {"loc": {"start": {"line": 160, "column": 0}, "end": {"line": 162, "column": 0}}, "type": "if", "locations": [{"start": {"line": 160, "column": 0}, "end": {"line": 162, "column": 0}}, {"start": {"line": 160, "column": 0}, "end": {"line": 162, "column": 0}}]}, "6": {"loc": {"start": {"line": 160, "column": 0}, "end": {"line": null, "column": -1}}, "type": "binary-expr", "locations": [{"start": {"line": 160, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 160, "column": 0}, "end": {"line": null, "column": -1}}]}, "7": {"loc": {"start": {"line": 164, "column": 0}, "end": {"line": 166, "column": 0}}, "type": "if", "locations": [{"start": {"line": 164, "column": 0}, "end": {"line": 166, "column": 0}}, {"start": {"line": 164, "column": 0}, "end": {"line": 166, "column": 0}}]}, "8": {"loc": {"start": {"line": 169, "column": 0}, "end": {"line": 177, "column": 0}}, "type": "if", "locations": [{"start": {"line": 169, "column": 0}, "end": {"line": 177, "column": 0}}, {"start": {"line": 169, "column": 0}, "end": {"line": 177, "column": 0}}]}, "9": {"loc": {"start": {"line": 171, "column": 0}, "end": {"line": 176, "column": 0}}, "type": "if", "locations": [{"start": {"line": 171, "column": 0}, "end": {"line": 176, "column": 0}}, {"start": {"line": 171, "column": 0}, "end": {"line": 176, "column": 0}}]}, "10": {"loc": {"start": {"line": 180, "column": 0}, "end": {"line": 189, "column": 0}}, "type": "if", "locations": [{"start": {"line": 180, "column": 0}, "end": {"line": 189, "column": 0}}, {"start": {"line": 180, "column": 0}, "end": {"line": 189, "column": 0}}]}, "11": {"loc": {"start": {"line": 180, "column": 0}, "end": {"line": null, "column": -1}}, "type": "binary-expr", "locations": [{"start": {"line": 180, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 180, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 180, "column": 0}, "end": {"line": null, "column": -1}}]}, "12": {"loc": {"start": {"line": 193, "column": 0}, "end": {"line": 204, "column": 0}}, "type": "if", "locations": [{"start": {"line": 193, "column": 0}, "end": {"line": 204, "column": 0}}, {"start": {"line": 193, "column": 0}, "end": {"line": 204, "column": 0}}]}, "13": {"loc": {"start": {"line": 193, "column": 0}, "end": {"line": null, "column": -1}}, "type": "binary-expr", "locations": [{"start": {"line": 193, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 193, "column": 0}, "end": {"line": null, "column": -1}}]}, "14": {"loc": {"start": {"line": 199, "column": 0}, "end": {"line": 202, "column": 0}}, "type": "if", "locations": [{"start": {"line": 199, "column": 0}, "end": {"line": 202, "column": 0}}, {"start": {"line": 199, "column": 0}, "end": {"line": 202, "column": 0}}]}, "15": {"loc": {"start": {"line": 154, "column": 0}, "end": {"line": null, "column": -1}}, "type": "if", "locations": [{"start": {"line": 154, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 154, "column": 0}, "end": {"line": null, "column": -1}}]}, "16": {"loc": {"start": {"line": 246, "column": 0}, "end": {"line": 274, "column": 0}}, "type": "if", "locations": [{"start": {"line": 246, "column": 0}, "end": {"line": 274, "column": 0}}, {"start": {"line": 246, "column": 0}, "end": {"line": 274, "column": 0}}]}, "17": {"loc": {"start": {"line": 244, "column": 0}, "end": {"line": null, "column": -1}}, "type": "if", "locations": [{"start": {"line": 244, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 244, "column": 0}, "end": {"line": null, "column": -1}}]}, "18": {"loc": {"start": {"line": 359, "column": 0}, "end": {"line": null, "column": -1}}, "type": "binary-expr", "locations": [{"start": {"line": 359, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 359, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 359, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 359, "column": 0}, "end": {"line": null, "column": -1}}]}, "19": {"loc": {"start": {"line": 366, "column": 0}, "end": {"line": 370, "column": 0}}, "type": "if", "locations": [{"start": {"line": 366, "column": 0}, "end": {"line": 370, "column": 0}}, {"start": {"line": 366, "column": 0}, "end": {"line": 370, "column": 0}}]}, "20": {"loc": {"start": {"line": 366, "column": 0}, "end": {"line": null, "column": -1}}, "type": "binary-expr", "locations": [{"start": {"line": 366, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 366, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 366, "column": 0}, "end": {"line": null, "column": -1}}]}, "21": {"loc": {"start": {"line": 382, "column": 0}, "end": {"line": 384, "column": 0}}, "type": "if", "locations": [{"start": {"line": 382, "column": 0}, "end": {"line": 384, "column": 0}}, {"start": {"line": 382, "column": 0}, "end": {"line": 384, "column": 0}}]}, "22": {"loc": {"start": {"line": 401, "column": 0}, "end": {"line": 403, "column": 0}}, "type": "if", "locations": [{"start": {"line": 401, "column": 0}, "end": {"line": 403, "column": 0}}, {"start": {"line": 401, "column": 0}, "end": {"line": 403, "column": 0}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0, "107": 0, "108": 0, "109": 0, "110": 0, "111": 0, "112": 0, "113": 0, "114": 0, "115": 0, "116": 0, "117": 0, "118": 0, "119": 0, "120": 0, "121": 0, "122": 0, "123": 0, "124": 0, "125": 0, "126": 0, "127": 0, "128": 0, "129": 0, "130": 0, "131": 0, "132": 0, "133": 0, "134": 0, "135": 0, "136": 0, "137": 0, "138": 0, "139": 0, "140": 0, "141": 0, "142": 0, "143": 0, "144": 0, "145": 0, "146": 0, "147": 0, "148": 0, "149": 0, "150": 0, "151": 0, "152": 0, "153": 0, "154": 0, "155": 0, "156": 0, "157": 0, "158": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0, 0, 0], "19": [0, 0], "20": [0, 0, 0], "21": [0, 0], "22": [0, 0]}}, "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\Contact.vue": {"path": "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\Contact.vue", "statementMap": {"0": {"start": {"line": 174, "column": 0}, "end": {"line": null, "column": -1}}, "1": {"start": {"line": 248, "column": 0}, "end": {"line": null, "column": -1}}, "2": {"start": {"line": 249, "column": 0}, "end": {"line": null, "column": -1}}, "3": {"start": {"line": 248, "column": 0}, "end": {"line": 252, "column": 0}}, "4": {"start": {"line": 252, "column": 0}, "end": {"line": null, "column": -1}}, "5": {"start": {"line": 254, "column": 0}, "end": {"line": null, "column": -1}}, "6": {"start": {"line": 255, "column": 0}, "end": {"line": null, "column": -1}}, "7": {"start": {"line": 258, "column": 0}, "end": {"line": null, "column": -1}}}, "fnMap": {"0": {"name": "data", "decl": {"start": {"line": 173, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 173, "column": 0}, "end": {"line": 246, "column": 0}}}, "1": {"name": "submitForm", "decl": {"start": {"line": 248, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 248, "column": 0}, "end": {"line": 266, "column": 0}}}}, "branchMap": {"0": {"loc": {"start": {"line": 248, "column": 0}, "end": {"line": 252, "column": 0}}, "type": "switch", "locations": [{"start": {"line": 248, "column": 0}, "end": {"line": 252, "column": 0}}, {"start": {"line": 248, "column": 0}, "end": {"line": 258, "column": 0}}, {"start": {"line": 248, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 248, "column": 0}, "end": {"line": null, "column": -1}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0, 0, 0]}}, "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\Footer.vue": {"path": "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\Footer.vue", "statementMap": {"0": {"start": {"line": 185, "column": 0}, "end": {"line": null, "column": -1}}}, "fnMap": {"0": {"name": "currentYear", "decl": {"start": {"line": 184, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 184, "column": 0}, "end": {"line": 186, "column": 0}}}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\Header.vue": {"path": "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\Header.vue", "statementMap": {"0": {"start": {"line": 67, "column": 0}, "end": {"line": null, "column": -1}}, "1": {"start": {"line": 73, "column": 0}, "end": {"line": null, "column": -1}}, "2": {"start": {"line": 74, "column": 0}, "end": {"line": null, "column": -1}}, "3": {"start": {"line": 77, "column": 0}, "end": {"line": null, "column": -1}}, "4": {"start": {"line": 81, "column": 0}, "end": {"line": null, "column": -1}}, "5": {"start": {"line": 84, "column": 0}, "end": {"line": null, "column": -1}}, "6": {"start": {"line": 87, "column": 0}, "end": {"line": null, "column": -1}}, "7": {"start": {"line": 88, "column": 0}, "end": {"line": null, "column": -1}}, "8": {"start": {"line": 89, "column": 0}, "end": {"line": 91, "column": 0}}, "9": {"start": {"line": 90, "column": 0}, "end": {"line": null, "column": -1}}}, "fnMap": {"0": {"name": "data", "decl": {"start": {"line": 66, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 66, "column": 0}, "end": {"line": 71, "column": 0}}}, "1": {"name": "mounted", "decl": {"start": {"line": 72, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 72, "column": 0}, "end": {"line": 75, "column": 0}}}, "2": {"name": "<PERSON><PERSON><PERSON><PERSON>", "decl": {"start": {"line": 76, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 76, "column": 0}, "end": {"line": 78, "column": 0}}}, "3": {"name": "toggleMenu", "decl": {"start": {"line": 80, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 80, "column": 0}, "end": {"line": 82, "column": 0}}}, "4": {"name": "closeMenu", "decl": {"start": {"line": 83, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 83, "column": 0}, "end": {"line": 85, "column": 0}}}, "5": {"name": "handleScroll", "decl": {"start": {"line": 86, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 86, "column": 0}, "end": {"line": 92, "column": 0}}}}, "branchMap": {"0": {"loc": {"start": {"line": 89, "column": 0}, "end": {"line": 91, "column": 0}}, "type": "if", "locations": [{"start": {"line": 89, "column": 0}, "end": {"line": 91, "column": 0}}, {"start": {"line": 89, "column": 0}, "end": {"line": 91, "column": 0}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0, 0]}}, "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\Home.vue": {"path": "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\Home.vue", "statementMap": {"0": {"start": {"line": 147, "column": 0}, "end": {"line": null, "column": -1}}}, "fnMap": {"0": {"name": "data", "decl": {"start": {"line": 146, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 146, "column": 0}, "end": {"line": 227, "column": 0}}}}, "branchMap": {}, "s": {"0": 0}, "f": {"0": 0}, "b": {}}, "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\News.vue": {"path": "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\News.vue", "statementMap": {"0": {"start": {"line": 207, "column": 0}, "end": {"line": null, "column": -1}}, "1": {"start": {"line": 340, "column": 0}, "end": {"line": null, "column": -1}}, "2": {"start": {"line": 342, "column": 0}, "end": {"line": null, "column": -1}}, "3": {"start": {"line": 343, "column": 0}, "end": {"line": null, "column": -1}}, "4": {"start": {"line": 344, "column": 0}, "end": {"line": null, "column": -1}}, "5": {"start": {"line": 347, "column": 0}, "end": {"line": 350, "column": 0}}, "6": {"start": {"line": 348, "column": 0}, "end": {"line": null, "column": -1}}, "7": {"start": {"line": 349, "column": 0}, "end": {"line": null, "column": -1}}, "8": {"start": {"line": 353, "column": 0}, "end": {"line": 361, "column": 0}}, "9": {"start": {"line": 354, "column": 0}, "end": {"line": null, "column": -1}}, "10": {"start": {"line": 355, "column": 0}, "end": {"line": null, "column": -1}}, "11": {"start": {"line": 360, "column": 0}, "end": {"line": null, "column": -1}}, "12": {"start": {"line": 363, "column": 0}, "end": {"line": null, "column": -1}}, "13": {"start": {"line": 364, "column": 0}, "end": {"line": null, "column": -1}}, "14": {"start": {"line": 367, "column": 0}, "end": {"line": null, "column": -1}}, "15": {"start": {"line": 368, "column": 0}, "end": {"line": null, "column": -1}}, "16": {"start": {"line": 369, "column": 0}, "end": {"line": null, "column": -1}}, "17": {"start": {"line": 372, "column": 0}, "end": {"line": null, "column": -1}}, "18": {"start": {"line": 377, "column": 0}, "end": {"line": null, "column": -1}}, "19": {"start": {"line": 380, "column": 0}, "end": {"line": null, "column": -1}}, "20": {"start": {"line": 385, "column": 0}, "end": {"line": null, "column": -1}}, "21": {"start": {"line": 386, "column": 0}, "end": {"line": null, "column": -1}}, "22": {"start": {"line": 389, "column": 0}, "end": {"line": null, "column": -1}}, "23": {"start": {"line": 390, "column": 0}, "end": {"line": null, "column": -1}}, "24": {"start": {"line": 397, "column": 0}, "end": {"line": null, "column": -1}}, "25": {"start": {"line": 398, "column": 0}, "end": {"line": null, "column": -1}}, "26": {"start": {"line": 404, "column": 0}, "end": {"line": null, "column": -1}}, "27": {"start": {"line": 405, "column": 0}, "end": {"line": null, "column": -1}}, "28": {"start": {"line": 408, "column": 0}, "end": {"line": null, "column": -1}}, "29": {"start": {"line": 410, "column": 0}, "end": {"line": null, "column": -1}}, "30": {"start": {"line": 413, "column": 0}, "end": {"line": null, "column": -1}}, "31": {"start": {"line": 416, "column": 0}, "end": {"line": null, "column": -1}}, "32": {"start": {"line": 418, "column": 0}, "end": {"line": null, "column": -1}}, "33": {"start": {"line": 419, "column": 0}, "end": {"line": null, "column": -1}}, "34": {"start": {"line": 420, "column": 0}, "end": {"line": null, "column": -1}}, "35": {"start": {"line": 421, "column": 0}, "end": {"line": null, "column": -1}}, "36": {"start": {"line": 426, "column": 0}, "end": {"line": null, "column": -1}}}, "fnMap": {"0": {"name": "data", "decl": {"start": {"line": 206, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 206, "column": 0}, "end": {"line": 337, "column": 0}}}, "1": {"name": "featuredArticles", "decl": {"start": {"line": 339, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 339, "column": 0}, "end": {"line": 341, "column": 0}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 340, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 340, "column": 0}, "end": {"line": null, "column": -1}}}, "3": {"name": "filteredArticles", "decl": {"start": {"line": 342, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 342, "column": 0}, "end": {"line": 365, "column": 0}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 348, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 348, "column": 0}, "end": {"line": null, "column": -1}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 349, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 349, "column": 0}, "end": {"line": null, "column": -1}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 355, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 355, "column": 0}, "end": {"line": null, "column": -1}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 360, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 360, "column": 0}, "end": {"line": null, "column": -1}}}, "8": {"name": "paginatedArticles", "decl": {"start": {"line": 366, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 366, "column": 0}, "end": {"line": 370, "column": 0}}}, "9": {"name": "totalPages", "decl": {"start": {"line": 371, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 371, "column": 0}, "end": {"line": 373, "column": 0}}}, "10": {"name": "activeCategory", "decl": {"start": {"line": 376, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 376, "column": 0}, "end": {"line": 378, "column": 0}}}, "11": {"name": "searchQuery", "decl": {"start": {"line": 379, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 379, "column": 0}, "end": {"line": 381, "column": 0}}}, "12": {"name": "getCategoryName", "decl": {"start": {"line": 384, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 384, "column": 0}, "end": {"line": 387, "column": 0}}}, "13": {"name": "(anonymous_14)", "decl": {"start": {"line": 385, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 385, "column": 0}, "end": {"line": null, "column": -1}}}, "14": {"name": "formatDate", "decl": {"start": {"line": 388, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 388, "column": 0}, "end": {"line": 395, "column": 0}}}, "15": {"name": "formatShortDate", "decl": {"start": {"line": 396, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 396, "column": 0}, "end": {"line": 402, "column": 0}}}, "16": {"name": "viewArticle", "decl": {"start": {"line": 403, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 403, "column": 0}, "end": {"line": 406, "column": 0}}}, "17": {"name": "changePage", "decl": {"start": {"line": 407, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 407, "column": 0}, "end": {"line": 411, "column": 0}}}, "18": {"name": "selectCategory", "decl": {"start": {"line": 412, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 412, "column": 0}, "end": {"line": 422, "column": 0}}}, "19": {"name": "testClick", "decl": {"start": {"line": 425, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 425, "column": 0}, "end": {"line": 427, "column": 0}}}}, "branchMap": {"0": {"loc": {"start": {"line": 347, "column": 0}, "end": {"line": 350, "column": 0}}, "type": "if", "locations": [{"start": {"line": 347, "column": 0}, "end": {"line": 350, "column": 0}}, {"start": {"line": 347, "column": 0}, "end": {"line": 350, "column": 0}}]}, "1": {"loc": {"start": {"line": 353, "column": 0}, "end": {"line": 361, "column": 0}}, "type": "if", "locations": [{"start": {"line": 353, "column": 0}, "end": {"line": 361, "column": 0}}, {"start": {"line": 353, "column": 0}, "end": {"line": 361, "column": 0}}]}, "2": {"loc": {"start": {"line": 355, "column": 0}, "end": {"line": null, "column": -1}}, "type": "binary-expr", "locations": [{"start": {"line": 355, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 355, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 355, "column": 0}, "end": {"line": null, "column": -1}}]}, "3": {"loc": {"start": {"line": 386, "column": 0}, "end": {"line": null, "column": -1}}, "type": "cond-expr", "locations": [{"start": {"line": 386, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 386, "column": 0}, "end": {"line": null, "column": -1}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0, 0], "3": [0, 0]}}, "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\Products.vue": {"path": "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\Products.vue", "statementMap": {"0": {"start": {"line": 201, "column": 0}, "end": {"line": null, "column": -1}}, "1": {"start": {"line": 412, "column": 0}, "end": {"line": null, "column": -1}}, "2": {"start": {"line": 413, "column": 0}, "end": {"line": 415, "column": 0}}, "3": {"start": {"line": 414, "column": 0}, "end": {"line": null, "column": -1}}, "4": {"start": {"line": 416, "column": 0}, "end": {"line": null, "column": -1}}, "5": {"start": {"line": 421, "column": 0}, "end": {"line": null, "column": -1}}, "6": {"start": {"line": 422, "column": 0}, "end": {"line": null, "column": -1}}}, "fnMap": {"0": {"name": "data", "decl": {"start": {"line": 200, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 200, "column": 0}, "end": {"line": 410, "column": 0}}}, "1": {"name": "filteredProducts", "decl": {"start": {"line": 412, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 412, "column": 0}, "end": {"line": 417, "column": 0}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 416, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 416, "column": 0}, "end": {"line": null, "column": -1}}}, "3": {"name": "showDemo", "decl": {"start": {"line": 420, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 420, "column": 0}, "end": {"line": 423, "column": 0}}}}, "branchMap": {"0": {"loc": {"start": {"line": 413, "column": 0}, "end": {"line": 415, "column": 0}}, "type": "if", "locations": [{"start": {"line": 413, "column": 0}, "end": {"line": 415, "column": 0}}, {"start": {"line": 413, "column": 0}, "end": {"line": 415, "column": 0}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0]}}, "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\sections\\PageHero.vue": {"path": "E:\\code\\动态时钟网站\\AuroraCore\\src\\components\\sections\\PageHero.vue", "statementMap": {"0": {"start": {"line": 35, "column": 0}, "end": {"line": null, "column": -1}}, "1": {"start": {"line": 36, "column": 0}, "end": {"line": null, "column": -1}}, "2": {"start": {"line": 39, "column": 0}, "end": {"line": 48, "column": 0}}, "3": {"start": {"line": 39, "column": 0}, "end": {"line": null, "column": -1}}, "4": {"start": {"line": 41, "column": 0}, "end": {"line": null, "column": -1}}, "5": {"start": {"line": 42, "column": 0}, "end": {"line": 48, "column": 0}}, "6": {"start": {"line": 43, "column": 0}, "end": {"line": null, "column": -1}}, "7": {"start": {"line": 44, "column": 0}, "end": {"line": null, "column": -1}}, "8": {"start": {"line": 45, "column": 0}, "end": {"line": null, "column": -1}}, "9": {"start": {"line": 46, "column": 0}, "end": {"line": 48, "column": 0}}, "10": {"start": {"line": 47, "column": 0}, "end": {"line": null, "column": -1}}, "11": {"start": {"line": 50, "column": 0}, "end": {"line": null, "column": -1}}, "12": {"start": {"line": 54, "column": 0}, "end": {"line": null, "column": -1}}}, "fnMap": {"0": {"name": "computedStyle", "decl": {"start": {"line": 34, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 34, "column": 0}, "end": {"line": 51, "column": 0}}}, "1": {"name": "hasBackgroundOverlay", "decl": {"start": {"line": 53, "column": 0}, "end": {"line": null, "column": -1}}, "loc": {"start": {"line": 53, "column": 0}, "end": {"line": 55, "column": 0}}}}, "branchMap": {"0": {"loc": {"start": {"line": 39, "column": 0}, "end": {"line": 48, "column": 0}}, "type": "if", "locations": [{"start": {"line": 39, "column": 0}, "end": {"line": 48, "column": 0}}, {"start": {"line": 39, "column": 0}, "end": {"line": 48, "column": 0}}]}, "1": {"loc": {"start": {"line": 42, "column": 0}, "end": {"line": 48, "column": 0}}, "type": "if", "locations": [{"start": {"line": 42, "column": 0}, "end": {"line": 48, "column": 0}}, {"start": {"line": 42, "column": 0}, "end": {"line": 48, "column": 0}}]}, "2": {"loc": {"start": {"line": 44, "column": 0}, "end": {"line": null, "column": -1}}, "type": "binary-expr", "locations": [{"start": {"line": 44, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 44, "column": 0}, "end": {"line": null, "column": -1}}]}, "3": {"loc": {"start": {"line": 45, "column": 0}, "end": {"line": null, "column": -1}}, "type": "binary-expr", "locations": [{"start": {"line": 45, "column": 0}, "end": {"line": null, "column": -1}}, {"start": {"line": 45, "column": 0}, "end": {"line": null, "column": -1}}]}, "4": {"loc": {"start": {"line": 46, "column": 0}, "end": {"line": 48, "column": 0}}, "type": "if", "locations": [{"start": {"line": 46, "column": 0}, "end": {"line": 48, "column": 0}}, {"start": {"line": 46, "column": 0}, "end": {"line": 48, "column": 0}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}}