<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/components/About.vue</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="..\..\prettify.css" />
    <link rel="stylesheet" href="..\..\base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(..\..\sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="..\..\index.html">All files</a> / <a href="index.html">src/components</a> About.vue
    </h1>
    <div class='clearfix'>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
410
411
412
413
414
415
416
417
418
419
420
421
422
423
424
425
426
427
428
429
430
431
432
433
434
435
436
437
438
439
440
441
442
443
444
445
446
447
448
449
450
451
452
453
454
455
456
457
458
459
460
461
462
463
464
465
466
467
468
469
470
471
472
473
474
475
476
477
478
479
480
481
482
483
484
485
486
487
488
489
490
491
492
493
494
495
496
497
498
499
500
501
502
503
504
505
506
507
508
509
510
511
512
513
514
515
516
517
518
519
520
521
522
523
524
525
526
527
528
529
530
531
532
533
534
535
536
537
538
539
540
541
542
543
544
545
546
547
548
549
550
551
552
553
554
555
556
557
558
559
560
561
562
563
564
565
566
567
568
569
570
571
572
573
574
575
576
577
578</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">&lt;template&gt;
  &lt;div class="about"&gt;
    &lt;!-- 动态渲染所有配置的区块 --&gt;
    &lt;component 
      v-for="section in pageConfig.sections" 
      :key="section.id"
      :is="getSectionComponent(section.type)"
      :section="section"
      v-show="section.enabled !== false"
    /&gt;
  &lt;/div&gt;
&lt;/template&gt;
&nbsp;
&lt;script&gt;
// 导入所有可用的区块组件
<span class="cstat-no" title="statement not covered" >import PageHero from './sections/PageHero.vue'</span>
<span class="cstat-no" title="statement not covered" >import ContentIntro from './sections/ContentIntro.vue'</span>
<span class="cstat-no" title="statement not covered" >import GridCards from './sections/GridCards.vue'</span>
<span class="cstat-no" title="statement not covered" >import TeamGrid from './sections/TeamGrid.vue'</span>
<span class="cstat-no" title="statement not covered" >import Timeline from './sections/Timeline.vue'</span>
<span class="cstat-no" title="statement not covered" >import StatsSection from './sections/StatsSection.vue'</span>
<span class="cstat-no" title="statement not covered" >import TestimonialsSection from './sections/TestimonialsSection.vue'</span>
<span class="cstat-no" title="statement not covered" >import ContactCTA from './sections/ContactCTA.vue'</span>
&nbsp;
export default {
  name: 'About',
  components: {
    PageHero,
    ContentIntro,
    GridCards,
    TeamGrid,
    Timeline,
    StatsSection,
    TestimonialsSection,
    ContactCTA
  },
  
<span class="fstat-no" title="function not covered" >  data () {</span>
<span class="cstat-no" title="statement not covered" >    return {</span>
      // 完全灵活的页面配置
      pageConfig: {
        // 页面基础信息
        meta: {
          title: '关于我们',
          description: '了解我们的团队、文化和发展历程'
        },
        
        // 动态区块配置 - 可以任意增减、重排序、选择不同组件类型
        sections: [
          {
            id: 'hero',
            type: 'hero',
            enabled: true,
            order: 1,
            config: {
              title: '关于我们',
              subtitle: '致力于为企业提供最优质的数字化解决方案',
              backgroundType: 'gradient', // gradient, image, solid
              backgroundConfig: {
                colors: ['var(--primary-color)', 'var(--primary-dark)'],
                direction: '135deg'
              },
              style: {
                padding: '120px 0 80px',
                textAlign: 'center',
                color: 'white'
              }
            }
          },
          
          {
            id: 'company-intro',
            type: 'content-intro',
            enabled: true,
            order: 2,
            config: {
              layout: 'split', // split, centered, full-width
              title: '我们的使命',
              content: [
                '自成立以来，我们始终专注于为企业提供创新的数字化解决方案。我们相信技术的力量能够改变世界，通过不断的创新和优质的服务，帮助客户在数字化时代取得成功。',
                '我们的团队由经验丰富的技术专家和业务顾问组成，拥有深厚的行业知识和丰富的项目经验。我们致力于理解客户的业务需求，提供量身定制的解决方案，确保每个项目的成功交付。'
              ],
              features: [
                {
                  id: 1,
                  title: '创新驱动',
                  description: '持续投入研发，保持技术领先',
                  icon: '💡'
                },
                {
                  id: 2,
                  title: '客户至上',
                  description: '以客户需求为导向，提供优质服务',
                  icon: '🎯'
                },
                {
                  id: 3,
                  title: '团队协作',
                  description: '发挥团队智慧，共创美好未来',
                  icon: '🤝'
                }
              ],
              mediaConfig: {
                type: 'icon', // icon, image, video
                content: '🏢',
                position: 'right' // left, right, top, bottom
              },
              style: {
                backgroundColor: 'white',
                padding: '80px 0'
              }
            }
          },
&nbsp;
          {
            id: 'company-stats',
            type: 'stats',
            enabled: true,
            order: 3,
            config: {
              layout: 'horizontal', // horizontal, grid, vertical
              title: '数据说话',
              subtitle: '用数字见证我们的成长',
              stats: [
                {
                  id: 1,
                  number: '500+',
                  label: '服务客户',
                  icon: '👥',
                  color: 'var(--primary-color)'
                },
                {
                  id: 2,
                  number: '1000+',
                  label: '成功项目',
                  icon: '🚀',
                  color: 'var(--success-color)'
                },
                {
                  id: 3,
                  number: '5年',
                  label: '行业经验',
                  icon: '⭐',
                  color: 'var(--warning-color)'
                },
                {
                  id: 4,
                  number: '99%',
                  label: '客户满意度',
                  icon: '❤️',
                  color: 'var(--danger-color)'
                }
              ],
              style: {
                backgroundColor: 'var(--gray-50)',
                padding: '60px 0'
              }
            }
          },
          
          {
            id: 'culture',
            type: 'grid-cards',
            enabled: true,
            order: 4,
            config: {
              title: '企业文化',
              subtitle: '我们的核心价值观指引着我们前进的方向',
              layout: 'grid', // grid, masonry, carousel
              columns: 4, // 1-6
              cardStyle: 'elevated', // flat, elevated, outlined
              items: [
                {
                  id: 1,
                  icon: '💡',
                  title: '创新精神',
                  description: '鼓励创新思维，勇于尝试新技术和新方法，持续改进和优化。',
                  color: 'var(--primary-color)'
                },
                {
                  id: 2,
                  icon: '🤝',
                  title: '诚信合作',
                  description: '以诚待人，诚信经营，与客户和合作伙伴建立长期信任关系。',
                  color: 'var(--success-color)'
                },
                {
                  id: 3,
                  icon: '🎯',
                  title: '追求卓越',
                  description: '精益求精，不断提升产品质量和服务水平，追求完美。',
                  color: 'var(--warning-color)'
                },
                {
                  id: 4,
                  icon: '🌱',
                  title: '持续学习',
                  description: '保持学习热情，紧跟技术发展趋势，提升专业能力。',
                  color: 'var(--info-color)'
                }
              ],
              style: {
                backgroundColor: 'white',
                padding: '80px 0'
              }
            }
          },
          
          {
            id: 'team',
            type: 'team-grid',
            enabled: true,
            order: 5,
            config: {
              title: '核心团队',
              subtitle: '优秀的团队是我们成功的基础',
              layout: 'grid', // grid, list, carousel
              columns: 4,
              showSocial: true,
              members: [
                {
                  id: 1,
                  name: '张伟',
                  position: '首席执行官',
                  avatar: '👨‍💼',
                  description: '10年企业管理经验，曾任职于多家知名科技公司，专注于企业数字化转型。',
                  social: {
                    linkedin: '#',
                    email: '<EMAIL>'
                  }
                },
                {
                  id: 2,
                  name: '李梅',
                  position: '技术总监',
                  avatar: '👩‍💻',
                  description: '资深技术专家，精通多种编程语言和架构设计，领导技术团队创新发展。',
                  social: {
                    github: '#',
                    email: '<EMAIL>'
                  }
                },
                {
                  id: 3,
                  name: '王强',
                  position: '产品经理',
                  avatar: '👨‍💻',
                  description: '丰富的产品设计经验，深度理解用户需求，打造优秀的产品体验。',
                  social: {
                    twitter: '#',
                    email: '<EMAIL>'
                  }
                },
                {
                  id: 4,
                  name: '赵丽',
                  position: '市场总监',
                  avatar: '👩‍💼',
                  description: '市场营销专家，具有敏锐的市场洞察力，成功推广多个项目。',
                  social: {
                    linkedin: '#',
                    email: '<EMAIL>'
                  }
                }
              ],
              style: {
                backgroundColor: 'var(--gray-50)',
                padding: '80px 0'
              }
            }
          },
          
          {
            id: 'timeline',
            type: 'timeline',
            enabled: true,
            order: 6,
            config: {
              title: '发展历程',
              subtitle: '见证我们一路走来的重要里程碑',
              layout: 'alternating', // alternating, left, right, vertical
              items: [
                {
                  id: 1,
                  year: '2019',
                  title: '公司成立',
                  description: '公司正式成立，专注于为中小企业提供数字化解决方案。',
                  icon: '🏢',
                  type: 'milestone'
                },
                {
                  id: 2,
                  year: '2020',
                  title: '产品发布',
                  description: '发布第一款企业管理系统，获得市场积极反馈。',
                  icon: '🚀',
                  type: 'product'
                },
                {
                  id: 3,
                  year: '2021',
                  title: '团队扩张',
                  description: '团队规模扩大到50人，建立了完善的研发体系。',
                  icon: '👥',
                  type: 'growth'
                },
                {
                  id: 4,
                  year: '2022',
                  title: '业务拓展',
                  description: '业务覆盖全国多个城市，服务客户超过500家。',
                  icon: '🌏',
                  type: 'expansion'
                },
                {
                  id: 5,
                  year: '2023',
                  title: '技术创新',
                  description: '引入AI技术，推出智能化产品线，获得多项技术专利。',
                  icon: '🤖',
                  type: 'innovation'
                },
                {
                  id: 6,
                  year: '2024',
                  title: '战略升级',
                  description: '启动数字化转型3.0战略，致力于成为行业领军企业。',
                  icon: '⚡',
                  type: 'strategy'
                }
              ],
              style: {
                backgroundColor: 'white',
                padding: '80px 0'
              }
            }
          },
&nbsp;
          {
            id: 'testimonials',
            type: 'testimonials',
            enabled: true,
            order: 7,
            config: {
              title: '客户评价',
              subtitle: '听听客户对我们的评价',
              layout: 'carousel', // grid, carousel, masonry
              autoplay: true,
              testimonials: [
                {
                  id: 1,
                  content: '合作非常愉快，团队专业度很高，项目交付及时，质量超出预期。',
                  author: '李总',
                  company: '某科技公司',
                  avatar: '👨‍💼',
                  rating: 5
                },
                {
                  id: 2,
                  content: '技术实力强，服务态度好，是值得信赖的合作伙伴。',
                  author: '王经理',
                  company: '某制造企业',
                  avatar: '👩‍💼',
                  rating: 5
                },
                {
                  id: 3,
                  content: '解决方案很有创新性，帮助我们大大提升了工作效率。',
                  author: '张主任',
                  company: '某互联网公司',
                  avatar: '👨‍💻',
                  rating: 5
                }
              ],
              style: {
                backgroundColor: 'var(--gray-50)',
                padding: '80px 0'
              }
            }
          },
&nbsp;
          {
            id: 'contact-cta',
            type: 'contact-cta',
            enabled: true,
            order: 8,
            config: {
              title: '开始合作',
              subtitle: '让我们一起创造更美好的未来',
              description: '无论您是需要数字化转型咨询，还是寻找技术解决方案，我们都随时为您服务。',
              primaryButton: {
                text: '立即咨询',
                link: '/contact',
                style: 'primary'
              },
              secondaryButton: {
                text: '了解更多',
                link: '/services',
                style: 'outline'
              },
              style: {
                backgroundColor: 'var(--primary-color)',
                color: 'white',
                padding: '80px 0'
              }
            }
          }
        ]
      }
    }
  },
&nbsp;
  // 生命周期：模拟从CMS/API获取页面配置
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >  async created () {</span></span>
<span class="cstat-no" title="statement not covered" >    console.log('🚀 开始加载关于我们页面配置...')</span>
    
    // 模拟API调用延迟
    await this.loadPageConfig()
    
<span class="cstat-no" title="statement not covered" >    console.log('✅ 页面配置加载完成:', this.pageConfig)</span>
  },
&nbsp;
  methods: {
    // 根据区块类型返回对应的组件名
<span class="fstat-no" title="function not covered" >    getSectionComponent (type) {</span>
<span class="cstat-no" title="statement not covered" >      const componentMap = {</span>
        'hero': 'PageHero',
        'content-intro': 'ContentIntro',
        'grid-cards': 'GridCards',
        'team-grid': 'TeamGrid',
        'timeline': 'Timeline',
        'stats': 'StatsSection',
        'testimonials': 'TestimonialsSection',
        'contact-cta': 'ContactCTA'
      }
      
<span class="cstat-no" title="statement not covered" >      return componentMap[type] || 'div'</span>
    },
&nbsp;
    // 模拟从API/CMS加载页面配置
<span class="cstat-no" title="statement not covered" ><span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >    async loadPageConfig () {</span></span></span>
      // 这里可以调用真实的API
      // const response = await api.getPageConfig('about')
      // this.pageConfig = response.data
      
      // 模拟网络延迟
      return new Promise(resolve =&gt; {
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >        setTimeout(() =&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >          console.log('📊 页面数据加载完成')</span>
<span class="cstat-no" title="statement not covered" >          resolve()</span>
        }, 300)
      })
    },
&nbsp;
    // 动态更新页面配置（用于CMS管理后台）
<span class="fstat-no" title="function not covered" >    updatePageConfig (newConfig) {</span>
<span class="cstat-no" title="statement not covered" >      this.pageConfig = {</span>
        ...this.pageConfig,
        ...newConfig
      }
<span class="cstat-no" title="statement not covered" >      console.log('🔄 页面配置已更新:', this.pageConfig)</span>
    },
&nbsp;
    // 添加新区块
<span class="fstat-no" title="function not covered" >    addSection (sectionConfig) {</span>
<span class="cstat-no" title="statement not covered" >      const newSection = {</span>
        id: 'section_' + Date.now(),
        order: this.pageConfig.sections.length + 1,
        enabled: true,
        ...sectionConfig
      }
      
<span class="cstat-no" title="statement not covered" >      this.pageConfig.sections.push(newSection)</span>
<span class="cstat-no" title="statement not covered" >      this.sortSections()</span>
<span class="cstat-no" title="statement not covered" >      console.log('➕ 已添加新区块:', newSection)</span>
    },
&nbsp;
    // 删除区块
<span class="fstat-no" title="function not covered" >    removeSection (sectionId) {</span>
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >      this.pageConfig.sections = this.pageConfig.sections.filter(</span></span>
<span class="cstat-no" title="statement not covered" >        section =&gt; section.id !== sectionId</span>
      )
<span class="cstat-no" title="statement not covered" >      console.log('❌ 已删除区块:', sectionId)</span>
    },
&nbsp;
    // 启用/禁用区块
<span class="fstat-no" title="function not covered" >    toggleSection (sectionId, enabled) {</span>
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >      const section = this.pageConfig.sections.find(s =&gt; s.id === sectionId)</span></span>
<span class="cstat-no" title="statement not covered" >      if (section) {</span>
<span class="cstat-no" title="statement not covered" >        section.enabled = enabled</span>
<span class="cstat-no" title="statement not covered" >        console.log('🔄 区块 ' + sectionId + ' ' + (enabled ? '已启用' : '已禁用'))</span>
      }
    },
&nbsp;
    // 重新排序区块
<span class="fstat-no" title="function not covered" >    reorderSection (sectionId, newOrder) {</span>
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >      const section = this.pageConfig.sections.find(s =&gt; s.id === sectionId)</span></span>
<span class="cstat-no" title="statement not covered" >      if (section) {</span>
<span class="cstat-no" title="statement not covered" >        section.order = newOrder</span>
<span class="cstat-no" title="statement not covered" >        this.sortSections()</span>
<span class="cstat-no" title="statement not covered" >        console.log('📋 区块顺序已更新')</span>
      }
    },
&nbsp;
    // 按order字段排序区块
<span class="fstat-no" title="function not covered" >    sortSections () {</span>
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >      this.pageConfig.sections.sort((a, b) =&gt; a.order - b.order)</span></span>
    },
&nbsp;
    // 导出页面配置（用于备份或迁移）
<span class="fstat-no" title="function not covered" >    exportConfig () {</span>
<span class="cstat-no" title="statement not covered" >      const config = JSON.stringify(this.pageConfig, null, 2)</span>
<span class="cstat-no" title="statement not covered" >      console.log('📄 页面配置导出:', config)</span>
<span class="cstat-no" title="statement not covered" >      return config</span>
    },
&nbsp;
    // 导入页面配置
<span class="fstat-no" title="function not covered" >    importConfig (configJson) {</span>
<span class="cstat-no" title="statement not covered" >      try {</span>
<span class="cstat-no" title="statement not covered" >        const config = JSON.parse(configJson)</span>
<span class="cstat-no" title="statement not covered" >        this.pageConfig = config</span>
<span class="cstat-no" title="statement not covered" >        console.log('📥 页面配置导入成功')</span>
      } catch (error) {
<span class="cstat-no" title="statement not covered" >        console.error('❌ 配置导入失败:', error)</span>
      }
    }
  }
}
&lt;/script&gt;
&nbsp;
&lt;style scoped&gt;
.about {
  min-height: 100vh;
}
&nbsp;
/* 全局区块样式 */
.about :deep(.section) {
  position: relative;
  overflow: hidden;
}
&nbsp;
.about :deep(.container) {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}
&nbsp;
.about :deep(.section-title) {
  text-align: center;
  margin-bottom: 3rem;
}
&nbsp;
.about :deep(.section-title h2) {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 0.5rem;
}
&nbsp;
.about :deep(.section-title p) {
  font-size: 1.125rem;
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
}
&nbsp;
/* 响应式设计 */
@media (max-width: 768px) {
  .about :deep(.section-title h2) {
    font-size: 2rem;
  }
  
  .about :deep(.section-title p) {
    font-size: 1rem;
  }
}
&lt;/style&gt;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="https://istanbul.js.org/" target="_blank">istanbul</a> at Sun Aug 17 2025 01:04:55 GMT+0800 (中国标准时间)
</div>
</div>
<script src="..\..\prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="..\..\sorter.js"></script>
</body>
</html>
