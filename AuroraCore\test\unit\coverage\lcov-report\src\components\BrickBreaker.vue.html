<!doctype html>
<html lang="en">
<head>
    <title>Code coverage report for src/components/BrickBreaker.vue</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="..\..\prettify.css" />
    <link rel="stylesheet" href="..\..\base.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(..\..\sort-arrow-sprite.png);
        }
    </style>
</head>
<body>
<div class='wrapper'>
  <div class='pad1'>
    <h1>
      <a href="..\..\index.html">All files</a> / <a href="index.html">src/components</a> BrickBreaker.vue
    </h1>
    <div class='clearfix'>
    </div>
  </div>
  <div class='status-line low'></div>
<pre><table class="coverage">
<tr><td class="line-count quiet">1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187
188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372
373
374
375
376
377
378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
410
411
412
413
414
415
416
417
418
419
420
421
422
423
424
425
426
427
428
429
430
431
432
433
434
435
436
437
438
439
440
441
442
443
444
445
446
447
448
449
450
451
452
453
454
455
456
457
458
459
460
461
462
463
464
465
466
467
468
469
470
471
472
473
474
475
476
477
478
479
480
481
482
483
484
485
486
487
488
489
490
491
492
493
494
495
496
497
498
499
500
501
502
503
504
505
506
507
508
509
510
511
512
513
514
515
516
517
518
519
520
521
522
523
524
525
526
527
528
529
530
531
532
533
534
535
536
537
538
539
540</td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">&lt;template&gt;
  &lt;div class="brick-breaker-container"&gt;
    &lt;div class="game-header"&gt;
      &lt;h1 class="game-title"&gt;🎮 手绘打砖块 🎮&lt;/h1&gt;
      &lt;div class="game-stats"&gt;
        &lt;span class="score"&gt;得分: {{ score }}&lt;/span&gt;
        &lt;span class="lives"&gt;生命: {{ lives }}&lt;/span&gt;
        &lt;span class="level"&gt;关卡: {{ level }}&lt;/span&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;div class="game-area" ref="gameArea"&gt;
      &lt;canvas 
        ref="gameCanvas" 
        :width="canvasWidth" 
        :height="canvasHeight"
        @mousemove="movePaddle"
        @click="startGame"
      &gt;&lt;/canvas&gt;
      
      &lt;div v-if="!gameStarted" class="start-overlay"&gt;
        &lt;div class="start-message"&gt;
          &lt;h2&gt;🎯 点击开始游戏 🎯&lt;/h2&gt;
          &lt;p&gt;移动鼠标控制挡板&lt;/p&gt;
          &lt;p&gt;打破所有砖块获得胜利！&lt;/p&gt;
        &lt;/div&gt;
      &lt;/div&gt;
      
      &lt;div v-if="gameOver" class="game-over-overlay"&gt;
        &lt;div class="game-over-message"&gt;
          &lt;h2 v-if="won"&gt;🎉 恭喜通关！ 🎉&lt;/h2&gt;
          &lt;h2 v-else&gt;💀 游戏结束 💀&lt;/h2&gt;
          &lt;p&gt;最终得分: {{ score }}&lt;/p&gt;
          &lt;button @click="resetGame" class="restart-btn"&gt;重新开始&lt;/button&gt;
        &lt;/div&gt;
      &lt;/div&gt;
    &lt;/div&gt;
    
    &lt;div class="game-controls"&gt;
      &lt;button @click="pauseGame" :disabled="!gameStarted || gameOver" class="control-btn"&gt;
        {{ paused ? '继续' : '暂停' }}
      &lt;/button&gt;
      &lt;button @click="resetGame" class="control-btn"&gt;重置游戏&lt;/button&gt;
    &lt;/div&gt;
  &lt;/div&gt;
&lt;/template&gt;
&nbsp;
&lt;script&gt;
export default {
  name: 'BrickBreaker',
<span class="fstat-no" title="function not covered" >  data() {</span>
<span class="cstat-no" title="statement not covered" >    return {</span>
      // Canvas 设置
      canvasWidth: 800,
      canvasHeight: 600,
      ctx: null,
      
      // 游戏状态
      gameStarted: false,
      gameOver: false,
      paused: false,
      won: false,
      score: 0,
      lives: 3,
      level: 1,
      
      // 游戏对象
      ball: {
        x: 400,
        y: 500,
        dx: 4,
        dy: -4,
        radius: 8
      },
      
      paddle: {
        x: 350,
        y: 550,
        width: 100,
        height: 15
      },
      
      bricks: [],
      
      // 游戏设置
      brickRows: 6,
      brickCols: 10,
      brickWidth: 70,
      brickHeight: 25,
      brickPadding: 5,
      brickOffsetTop: 60,
      brickOffsetLeft: 35,
      
      // 动画
      animationId: null,
      
      // 手绘效果
      wobbleOffset: 0
    }
  },
  
<span class="fstat-no" title="function not covered" >  mounted() {</span>
<span class="cstat-no" title="statement not covered" >    this.initGame()</span>
  },
  
<span class="fstat-no" title="function not covered" >  beforeUnmount() {</span>
<span class="cstat-no" title="statement not covered" >    if (this.animationId) {</span>
<span class="cstat-no" title="statement not covered" >      cancelAnimationFrame(this.animationId)</span>
    }
  },
  
  methods: {
<span class="fstat-no" title="function not covered" >    initGame() {</span>
<span class="cstat-no" title="statement not covered" >      const canvas = this.$refs.gameCanvas</span>
<span class="cstat-no" title="statement not covered" >      this.ctx = canvas.getContext('2d')</span>
<span class="cstat-no" title="statement not covered" >      this.createBricks()</span>
<span class="cstat-no" title="statement not covered" >      this.draw()</span>
    },
    
<span class="fstat-no" title="function not covered" >    createBricks() {</span>
<span class="cstat-no" title="statement not covered" >      this.bricks = []</span>
<span class="cstat-no" title="statement not covered" >      const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']</span>
      
<span class="cstat-no" title="statement not covered" >      for (let r = 0; r &lt; this.brickRows; r++) {</span>
<span class="cstat-no" title="statement not covered" >        for (let c = 0; c &lt; this.brickCols; c++) {</span>
<span class="cstat-no" title="statement not covered" >          this.bricks.push({</span>
            x: c * (this.brickWidth + this.brickPadding) + this.brickOffsetLeft,
            y: r * (this.brickHeight + this.brickPadding) + this.brickOffsetTop,
            width: this.brickWidth,
            height: this.brickHeight,
            color: colors[r % colors.length],
            visible: true,
            wobble: Math.random() * 0.5
          })
        }
      }
    },
    
<span class="fstat-no" title="function not covered" >    startGame() {</span>
<span class="cstat-no" title="statement not covered" >      if (!this.gameStarted &amp;&amp; !this.gameOver) {</span>
<span class="cstat-no" title="statement not covered" >        this.gameStarted = true</span>
<span class="cstat-no" title="statement not covered" >        this.gameLoop()</span>
      }
    },
    
<span class="fstat-no" title="function not covered" >    gameLoop() {</span>
<span class="cstat-no" title="statement not covered" >      if (!this.paused &amp;&amp; !this.gameOver) {</span>
<span class="cstat-no" title="statement not covered" >        this.update()</span>
<span class="cstat-no" title="statement not covered" >        this.draw()</span>
<span class="cstat-no" title="statement not covered" >        this.animationId = requestAnimationFrame(this.gameLoop)</span>
      }
    },
    
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >    update() {</span></span>
      // 更新球的位置
<span class="cstat-no" title="statement not covered" >      this.ball.x += this.ball.dx</span>
<span class="cstat-no" title="statement not covered" >      this.ball.y += this.ball.dy</span>
      
      // 球与墙壁碰撞
<span class="cstat-no" title="statement not covered" >      if (this.ball.x + this.ball.radius &gt; this.canvasWidth || this.ball.x - this.ball.radius &lt; 0) {</span>
<span class="cstat-no" title="statement not covered" >        this.ball.dx = -this.ball.dx</span>
      }
      
<span class="cstat-no" title="statement not covered" >      if (this.ball.y - this.ball.radius &lt; 0) {</span>
<span class="cstat-no" title="statement not covered" >        this.ball.dy = -this.ball.dy</span>
      }
      
      // 球掉落
<span class="cstat-no" title="statement not covered" >      if (this.ball.y + this.ball.radius &gt; this.canvasHeight) {</span>
<span class="cstat-no" title="statement not covered" >        this.lives--</span>
<span class="cstat-no" title="statement not covered" >        if (this.lives &lt;= 0) {</span>
<span class="cstat-no" title="statement not covered" >          this.gameOver = true</span>
<span class="cstat-no" title="statement not covered" >          this.won = false</span>
        } else {
<span class="cstat-no" title="statement not covered" >          this.resetBall()</span>
        }
      }
      
      // 球与挡板碰撞
<span class="cstat-no" title="statement not covered" >      if (this.ball.y + this.ball.radius &gt; this.paddle.y &amp;&amp;</span>
          this.ball.x &gt; this.paddle.x &amp;&amp;
          this.ball.x &lt; this.paddle.x + this.paddle.width) {
        // 根据击中位置改变角度
<span class="cstat-no" title="statement not covered" >        const hitPos = (this.ball.x - this.paddle.x) / this.paddle.width</span>
<span class="cstat-no" title="statement not covered" >        const angle = (hitPos - 0.5) * Math.PI / 3</span>
<span class="cstat-no" title="statement not covered" >        const speed = Math.sqrt(this.ball.dx * this.ball.dx + this.ball.dy * this.ball.dy)</span>
<span class="cstat-no" title="statement not covered" >        this.ball.dx = Math.sin(angle) * speed</span>
<span class="cstat-no" title="statement not covered" >        this.ball.dy = -Math.abs(Math.cos(angle) * speed)</span>
      }
      
      // 球与砖块碰撞
<span class="cstat-no" title="statement not covered" ><span class="cstat-no" title="statement not covered" >      for (let brick of this.bricks) {</span></span>
<span class="cstat-no" title="statement not covered" >        if (brick.visible &amp;&amp; this.ballBrickCollision(brick)) {</span>
<span class="cstat-no" title="statement not covered" >          this.ball.dy = -this.ball.dy</span>
<span class="cstat-no" title="statement not covered" >          brick.visible = false</span>
<span class="cstat-no" title="statement not covered" >          this.score += 10</span>
          
          // 检查是否获胜
<span class="cstat-no" title="statement not covered" ><span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >          if (this.bricks.every(b =&gt; !b.visible)) {</span></span></span>
<span class="cstat-no" title="statement not covered" >            this.gameOver = true</span>
<span class="cstat-no" title="statement not covered" >            this.won = true</span>
          }
<span class="cstat-no" title="statement not covered" >          break</span>
        }
      }
      
      // 更新手绘效果
<span class="cstat-no" title="statement not covered" >      this.wobbleOffset += 0.1</span>
    },
&nbsp;
<span class="fstat-no" title="function not covered" >    draw() {</span>
      // 清空画布
<span class="cstat-no" title="statement not covered" >      this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)</span>
&nbsp;
      // 绘制背景
<span class="cstat-no" title="statement not covered" >      this.drawBackground()</span>
&nbsp;
      // 绘制砖块
<span class="cstat-no" title="statement not covered" >      this.drawBricks()</span>
&nbsp;
      // 绘制挡板
<span class="cstat-no" title="statement not covered" >      this.drawPaddle()</span>
&nbsp;
      // 绘制球
<span class="cstat-no" title="statement not covered" >      this.drawBall()</span>
    },
&nbsp;
<span class="fstat-no" title="function not covered" >    drawBackground() {</span>
      // 手绘风格的背景
<span class="cstat-no" title="statement not covered" >      this.ctx.fillStyle = '#f0f8ff'</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)</span>
&nbsp;
      // 添加一些手绘风格的装饰
<span class="cstat-no" title="statement not covered" >      this.ctx.strokeStyle = '#e0e0e0'</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.lineWidth = 2</span>
<span class="cstat-no" title="statement not covered" >      for (let i = 0; i &lt; 10; i++) {</span>
<span class="cstat-no" title="statement not covered" >        this.ctx.beginPath()</span>
<span class="cstat-no" title="statement not covered" >        this.ctx.moveTo(Math.random() * this.canvasWidth, Math.random() * this.canvasHeight)</span>
<span class="cstat-no" title="statement not covered" >        this.ctx.lineTo(Math.random() * this.canvasWidth, Math.random() * this.canvasHeight)</span>
<span class="cstat-no" title="statement not covered" >        this.ctx.stroke()</span>
      }
    },
&nbsp;
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >    drawBricks() {</span></span>
<span class="cstat-no" title="statement not covered" ><span class="cstat-no" title="statement not covered" >      for (let brick of this.bricks) {</span></span>
<span class="cstat-no" title="statement not covered" >        if (brick.visible) {</span>
<span class="cstat-no" title="statement not covered" >          this.ctx.save()</span>
&nbsp;
          // 手绘风格的砖块
<span class="cstat-no" title="statement not covered" >          const wobble = Math.sin(this.wobbleOffset + brick.wobble) * 2</span>
<span class="cstat-no" title="statement not covered" >          this.ctx.translate(brick.x + wobble, brick.y + wobble * 0.5)</span>
<span class="cstat-no" title="statement not covered" >          this.ctx.rotate((Math.sin(this.wobbleOffset * 0.5) * 0.05))</span>
&nbsp;
          // 砖块主体
<span class="cstat-no" title="statement not covered" >          this.ctx.fillStyle = brick.color</span>
<span class="cstat-no" title="statement not covered" >          this.ctx.fillRect(0, 0, brick.width, brick.height)</span>
&nbsp;
          // 手绘边框
<span class="cstat-no" title="statement not covered" >          this.ctx.strokeStyle = '#333'</span>
<span class="cstat-no" title="statement not covered" >          this.ctx.lineWidth = 3</span>
<span class="cstat-no" title="statement not covered" >          this.ctx.beginPath()</span>
<span class="cstat-no" title="statement not covered" >          this.ctx.moveTo(2, 2)</span>
<span class="cstat-no" title="statement not covered" >          this.ctx.lineTo(brick.width - 2, 3)</span>
<span class="cstat-no" title="statement not covered" >          this.ctx.lineTo(brick.width - 1, brick.height - 2)</span>
<span class="cstat-no" title="statement not covered" >          this.ctx.lineTo(1, brick.height - 1)</span>
<span class="cstat-no" title="statement not covered" >          this.ctx.closePath()</span>
<span class="cstat-no" title="statement not covered" >          this.ctx.stroke()</span>
&nbsp;
          // 高光效果
<span class="cstat-no" title="statement not covered" >          this.ctx.fillStyle = 'rgba(255,255,255,0.3)'</span>
<span class="cstat-no" title="statement not covered" >          this.ctx.fillRect(5, 5, brick.width - 15, 8)</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >          this.ctx.restore()</span>
        }
      }
    },
&nbsp;
<span class="fstat-no" title="function not covered" >    drawPaddle() {</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.save()</span>
&nbsp;
      // 手绘风格的挡板
<span class="cstat-no" title="statement not covered" >      const wobble = Math.sin(this.wobbleOffset * 2) * 1</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.translate(this.paddle.x + wobble, this.paddle.y)</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.rotate(Math.sin(this.wobbleOffset * 0.3) * 0.02)</span>
&nbsp;
      // 挡板主体
<span class="cstat-no" title="statement not covered" >      this.ctx.fillStyle = '#8B4513'</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.fillRect(0, 0, this.paddle.width, this.paddle.height)</span>
&nbsp;
      // 手绘边框
<span class="cstat-no" title="statement not covered" >      this.ctx.strokeStyle = '#333'</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.lineWidth = 3</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.beginPath()</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.moveTo(2, 2)</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.lineTo(this.paddle.width - 2, 1)</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.lineTo(this.paddle.width - 1, this.paddle.height - 2)</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.lineTo(1, this.paddle.height - 1)</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.closePath()</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.stroke()</span>
&nbsp;
      // 木纹效果
<span class="cstat-no" title="statement not covered" >      this.ctx.strokeStyle = '#654321'</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.lineWidth = 1</span>
<span class="cstat-no" title="statement not covered" >      for (let i = 0; i &lt; 3; i++) {</span>
<span class="cstat-no" title="statement not covered" >        this.ctx.beginPath()</span>
<span class="cstat-no" title="statement not covered" >        this.ctx.moveTo(10 + i * 25, 3)</span>
<span class="cstat-no" title="statement not covered" >        this.ctx.lineTo(15 + i * 25, this.paddle.height - 3)</span>
<span class="cstat-no" title="statement not covered" >        this.ctx.stroke()</span>
      }
&nbsp;
<span class="cstat-no" title="statement not covered" >      this.ctx.restore()</span>
    },
&nbsp;
<span class="fstat-no" title="function not covered" >    drawBall() {</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.save()</span>
&nbsp;
      // 手绘风格的球
<span class="cstat-no" title="statement not covered" >      const wobble = Math.sin(this.wobbleOffset * 3) * 0.5</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.translate(this.ball.x + wobble, this.ball.y + wobble)</span>
&nbsp;
      // 球的主体
<span class="cstat-no" title="statement not covered" >      this.ctx.fillStyle = '#FF4444'</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.beginPath()</span>
&nbsp;
      // 手绘圆形（不完美的圆）
<span class="cstat-no" title="statement not covered" >      const points = 16</span>
<span class="cstat-no" title="statement not covered" >      const angleStep = (Math.PI * 2) / points</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.moveTo(</span>
        this.ball.radius + Math.sin(this.wobbleOffset) * 0.5,
        0
      )
&nbsp;
<span class="cstat-no" title="statement not covered" >      for (let i = 1; i &lt;= points; i++) {</span>
<span class="cstat-no" title="statement not covered" >        const angle = i * angleStep</span>
<span class="cstat-no" title="statement not covered" >        const radius = this.ball.radius + Math.sin(angle * 3 + this.wobbleOffset) * 0.8</span>
<span class="cstat-no" title="statement not covered" >        const x = Math.cos(angle) * radius</span>
<span class="cstat-no" title="statement not covered" >        const y = Math.sin(angle) * radius</span>
<span class="cstat-no" title="statement not covered" >        this.ctx.lineTo(x, y)</span>
      }
&nbsp;
<span class="cstat-no" title="statement not covered" >      this.ctx.closePath()</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.fill()</span>
&nbsp;
      // 球的边框
<span class="cstat-no" title="statement not covered" >      this.ctx.strokeStyle = '#333'</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.lineWidth = 2</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.stroke()</span>
&nbsp;
      // 高光
<span class="cstat-no" title="statement not covered" >      this.ctx.fillStyle = 'rgba(255,255,255,0.6)'</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.beginPath()</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.arc(-2, -2, 3, 0, Math.PI * 2)</span>
<span class="cstat-no" title="statement not covered" >      this.ctx.fill()</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      this.ctx.restore()</span>
    },
&nbsp;
<span class="fstat-no" title="function not covered" >    ballBrickCollision(brick) {</span>
<span class="cstat-no" title="statement not covered" >      return this.ball.x + this.ball.radius &gt; brick.x &amp;&amp;</span>
             this.ball.x - this.ball.radius &lt; brick.x + brick.width &amp;&amp;
             this.ball.y + this.ball.radius &gt; brick.y &amp;&amp;
             this.ball.y - this.ball.radius &lt; brick.y + brick.height
    },
&nbsp;
<span class="fstat-no" title="function not covered" >    movePaddle(event) {</span>
<span class="cstat-no" title="statement not covered" >      if (this.gameStarted &amp;&amp; !this.gameOver &amp;&amp; !this.paused) {</span>
<span class="cstat-no" title="statement not covered" >        const rect = this.$refs.gameCanvas.getBoundingClientRect()</span>
<span class="cstat-no" title="statement not covered" >        const mouseX = event.clientX - rect.left</span>
<span class="cstat-no" title="statement not covered" >        this.paddle.x = Math.max(0, Math.min(mouseX - this.paddle.width / 2, this.canvasWidth - this.paddle.width))</span>
      }
    },
&nbsp;
<span class="fstat-no" title="function not covered" >    resetBall() {</span>
<span class="cstat-no" title="statement not covered" >      this.ball.x = this.canvasWidth / 2</span>
<span class="cstat-no" title="statement not covered" >      this.ball.y = this.canvasHeight - 100</span>
<span class="cstat-no" title="statement not covered" >      this.ball.dx = (Math.random() - 0.5) * 8</span>
<span class="cstat-no" title="statement not covered" >      this.ball.dy = -4</span>
    },
&nbsp;
<span class="fstat-no" title="function not covered" >    pauseGame() {</span>
<span class="cstat-no" title="statement not covered" >      this.paused = !this.paused</span>
<span class="cstat-no" title="statement not covered" >      if (!this.paused) {</span>
<span class="cstat-no" title="statement not covered" >        this.gameLoop()</span>
      }
    },
&nbsp;
<span class="fstat-no" title="function not covered" >    resetGame() {</span>
<span class="cstat-no" title="statement not covered" >      this.gameStarted = false</span>
<span class="cstat-no" title="statement not covered" >      this.gameOver = false</span>
<span class="cstat-no" title="statement not covered" >      this.paused = false</span>
<span class="cstat-no" title="statement not covered" >      this.won = false</span>
<span class="cstat-no" title="statement not covered" >      this.score = 0</span>
<span class="cstat-no" title="statement not covered" >      this.lives = 3</span>
<span class="cstat-no" title="statement not covered" >      this.level = 1</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      this.resetBall()</span>
<span class="cstat-no" title="statement not covered" >      this.paddle.x = (this.canvasWidth - this.paddle.width) / 2</span>
<span class="cstat-no" title="statement not covered" >      this.createBricks()</span>
<span class="cstat-no" title="statement not covered" >      this.draw()</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      if (this.animationId) {</span>
<span class="cstat-no" title="statement not covered" >        cancelAnimationFrame(this.animationId)</span>
      }
    }
  }
}
&lt;/script&gt;
&nbsp;
&lt;style scoped&gt;
.brick-breaker-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  font-family: 'Comic Sans MS', cursive, sans-serif;
}
&nbsp;
.game-header {
  text-align: center;
  margin-bottom: 20px;
}
&nbsp;
.game-title {
  font-size: 2.5rem;
  color: #fff;
  text-shadow: 3px 3px 0px #333;
  margin: 0;
  transform: rotate(-2deg);
}
&nbsp;
.game-stats {
  display: flex;
  gap: 30px;
  justify-content: center;
  margin-top: 10px;
}
&nbsp;
.game-stats span {
  background: #fff;
  padding: 8px 15px;
  border-radius: 20px;
  border: 3px solid #333;
  font-weight: bold;
  font-size: 1.1rem;
  transform: rotate(1deg);
  box-shadow: 3px 3px 0px #333;
}
&nbsp;
.game-area {
  position: relative;
  border: 5px solid #333;
  border-radius: 15px;
  background: #f0f8ff;
  box-shadow: 0 0 20px rgba(0,0,0,0.3);
  overflow: hidden;
}
&nbsp;
canvas {
  display: block;
  cursor: none;
}
&nbsp;
.start-overlay,
.game-over-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
}
&nbsp;
.start-message h2,
.game-over-message h2 {
  font-size: 2rem;
  margin-bottom: 20px;
  text-shadow: 2px 2px 0px #333;
}
&nbsp;
.start-message p {
  font-size: 1.2rem;
  margin: 10px 0;
}
&nbsp;
.restart-btn {
  background: #4ECDC4;
  border: 3px solid #333;
  border-radius: 15px;
  padding: 12px 25px;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  transform: rotate(-1deg);
  transition: all 0.2s;
  box-shadow: 3px 3px 0px #333;
}
&nbsp;
.restart-btn:hover {
  transform: rotate(-1deg) scale(1.05);
  background: #45B7D1;
}
&nbsp;
.game-controls {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}
&nbsp;
.control-btn {
  background: #FF6B6B;
  border: 3px solid #333;
  border-radius: 15px;
  padding: 10px 20px;
  font-size: 1.1rem;
  font-weight: bold;
  color: white;
  cursor: pointer;
  transform: rotate(1deg);
  transition: all 0.2s;
  box-shadow: 3px 3px 0px #333;
}
&nbsp;
.control-btn:hover:not(:disabled) {
  transform: rotate(1deg) scale(1.05);
  background: #FF5252;
}
&nbsp;
.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
&lt;/style&gt;
&nbsp;</pre></td></tr>
</table></pre>
<div class='push'></div><!-- for sticky footer -->
</div><!-- /wrapper -->
<div class='footer quiet pad2 space-top1 center small'>
  Code coverage
  generated by <a href="https://istanbul.js.org/" target="_blank">istanbul</a> at Sun Aug 17 2025 01:04:55 GMT+0800 (中国标准时间)
</div>
</div>
<script src="..\..\prettify.js"></script>
<script>
window.onload = function () {
        if (typeof prettyPrint === 'function') {
            prettyPrint();
        }
};
</script>
<script src="..\..\sorter.js"></script>
</body>
</html>
