import Vue from 'vue'
import BrickBreaker from '@/components/BrickBreaker'

describe('BrickBreaker.vue', () => {
  it('should render correct contents', () => {
    const Constructor = Vue.extend(BrickBreaker)
    const vm = new Constructor().$mount()
    expect(vm.$el.querySelector('.game-title').textContent)
      .toEqual('🎮 手绘打砖块 🎮')
  })

  it('should initialize game state correctly', () => {
    const Constructor = Vue.extend(BrickBreaker)
    const vm = new Constructor().$mount()
    
    expect(vm.gameStarted).toBe(false)
    expect(vm.gameOver).toBe(false)
    expect(vm.score).toBe(0)
    expect(vm.lives).toBe(3)
    expect(vm.level).toBe(1)
  })

  it('should create bricks correctly', () => {
    const Constructor = Vue.extend(BrickBreaker)
    const vm = new Constructor().$mount()
    
    expect(vm.bricks.length).toBe(vm.brickRows * vm.brickCols)
    expect(vm.bricks[0]).toHaveProperty('x')
    expect(vm.bricks[0]).toHaveProperty('y')
    expect(vm.bricks[0]).toHaveProperty('visible')
    expect(vm.bricks[0].visible).toBe(true)
  })

  it('should reset game correctly', () => {
    const Constructor = Vue.extend(BrickBreaker)
    const vm = new Constructor().$mount()
    
    // 修改一些状态
    vm.gameStarted = true
    vm.score = 100
    vm.lives = 1
    
    // 重置游戏
    vm.resetGame()
    
    expect(vm.gameStarted).toBe(false)
    expect(vm.gameOver).toBe(false)
    expect(vm.score).toBe(0)
    expect(vm.lives).toBe(3)
    expect(vm.level).toBe(1)
  })

  it('should handle ball-brick collision correctly', () => {
    const Constructor = Vue.extend(BrickBreaker)
    const vm = new Constructor().$mount()
    
    const brick = {
      x: 100,
      y: 100,
      width: 70,
      height: 25,
      visible: true
    }
    
    // 球在砖块内
    vm.ball.x = 120
    vm.ball.y = 110
    vm.ball.radius = 8
    
    expect(vm.ballBrickCollision(brick)).toBe(true)
    
    // 球在砖块外
    vm.ball.x = 50
    vm.ball.y = 50
    
    expect(vm.ballBrickCollision(brick)).toBe(false)
  })
})
