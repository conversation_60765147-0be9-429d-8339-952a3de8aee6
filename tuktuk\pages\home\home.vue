<template>
	<view class="game-container">
		<!-- 游戏标题 -->
		<view class="game-header">
			<text class="game-title">打砖块游戏</text>
			<view class="game-info">
				<text class="score">得分: {{ score }}</text>
				<text class="lives">生命: {{ lives }}</text>
			</view>
		</view>

		<!-- 游戏画布 -->
		<canvas
			canvas-id="gameCanvas"
			class="game-canvas"
			@touchstart="handleTouchStart"
			@touchmove="handleTouchMove"
		></canvas>

		<!-- 游戏控制按钮 -->
		<view class="game-controls">
			<button v-if="!gameRunning && !gameOver" @click="startGame" class="btn start-btn">开始游戏</button>
			<button v-if="gameRunning" @click="pauseGame" class="btn pause-btn">{{ gamePaused ? '继续' : '暂停' }}</button>
			<button v-if="gameOver" @click="resetGame" class="btn reset-btn">重新开始</button>
		</view>

		<!-- 游戏结束提示 -->
		<view v-if="gameOver" class="game-over">
			<text class="game-over-text">{{ gameWon ? '恭喜通关！' : '游戏结束' }}</text>
			<text class="final-score">最终得分: {{ score }}</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 游戏状态
				gameRunning: false,
				gamePaused: false,
				gameOver: false,
				gameWon: false,
				score: 0,
				lives: 3,

				// 画布相关
				ctx: null,
				canvasWidth: 350,
				canvasHeight: 500,

				// 球相关
				ball: {
					x: 175,
					y: 400,
					dx: 3,
					dy: -3,
					radius: 8
				},

				// 挡板相关
				paddle: {
					x: 150,
					y: 450,
					width: 80,
					height: 10,
					speed: 6
				},

				// 砖块相关
				bricks: [],
				brickRows: 6,
				brickCols: 8,
				brickWidth: 40,
				brickHeight: 20,
				brickPadding: 3,
				brickOffsetTop: 60,
				brickOffsetLeft: 10,

				// 动画相关
				animationId: null,

				// 触摸相关
				touchStartX: 0
			}
		},

		mounted() {
			this.initCanvas();
			this.initBricks();
		},

		beforeDestroy() {
			if (this.animationId) {
				cancelAnimationFrame(this.animationId);
			}
		},

		methods: {
			// 初始化画布
			initCanvas() {
				const query = uni.createSelectorQuery().in(this);
				query.select('#gameCanvas').fields({ node: true, size: true }).exec((res) => {
					if (res[0]) {
						const canvas = res[0].node;
						this.ctx = canvas.getContext('2d');

						// 设置画布尺寸
						canvas.width = this.canvasWidth;
						canvas.height = this.canvasHeight;

						this.drawGame();
					}
				});
			},

			// 初始化砖块
			initBricks() {
				this.bricks = [];
				for (let r = 0; r < this.brickRows; r++) {
					this.bricks[r] = [];
					for (let c = 0; c < this.brickCols; c++) {
						this.bricks[r][c] = {
							x: c * (this.brickWidth + this.brickPadding) + this.brickOffsetLeft,
							y: r * (this.brickHeight + this.brickPadding) + this.brickOffsetTop,
							status: 1,
							color: this.getBrickColor(r)
						};
					}
				}
			},

			// 获取砖块颜色
			getBrickColor(row) {
				const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
				return colors[row % colors.length];
			},

			// 开始游戏
			startGame() {
				this.gameRunning = true;
				this.gameOver = false;
				this.gameWon = false;
				this.gamePaused = false;
				this.resetBallAndPaddle();
				this.gameLoop();
			},

			// 暂停/继续游戏
			pauseGame() {
				this.gamePaused = !this.gamePaused;
				if (!this.gamePaused) {
					this.gameLoop();
				}
			},

			// 重置游戏
			resetGame() {
				this.gameRunning = false;
				this.gameOver = false;
				this.gameWon = false;
				this.gamePaused = false;
				this.score = 0;
				this.lives = 3;
				this.initBricks();
				this.resetBallAndPaddle();
				this.drawGame();
			},

			// 重置球和挡板位置
			resetBallAndPaddle() {
				this.ball.x = this.canvasWidth / 2;
				this.ball.y = this.canvasHeight - 80;
				this.ball.dx = 3;
				this.ball.dy = -3;
				this.paddle.x = (this.canvasWidth - this.paddle.width) / 2;
			},

			// 游戏主循环
			gameLoop() {
				if (!this.gameRunning || this.gamePaused) return;

				this.updateGame();
				this.drawGame();

				this.animationId = requestAnimationFrame(() => this.gameLoop());
			},

			// 更新游戏状态
			updateGame() {
				// 移动球
				this.ball.x += this.ball.dx;
				this.ball.y += this.ball.dy;

				// 球与墙壁碰撞检测
				if (this.ball.x + this.ball.radius > this.canvasWidth || this.ball.x - this.ball.radius < 0) {
					this.ball.dx = -this.ball.dx;
				}
				if (this.ball.y - this.ball.radius < 0) {
					this.ball.dy = -this.ball.dy;
				}

				// 球掉落检测
				if (this.ball.y + this.ball.radius > this.canvasHeight) {
					this.lives--;
					if (this.lives <= 0) {
						this.gameOver = true;
						this.gameRunning = false;
					} else {
						this.resetBallAndPaddle();
					}
				}

				// 球与挡板碰撞检测
				if (this.ball.y + this.ball.radius > this.paddle.y &&
					this.ball.x > this.paddle.x &&
					this.ball.x < this.paddle.x + this.paddle.width) {
					this.ball.dy = -this.ball.dy;

					// 根据球击中挡板的位置调整角度
					const hitPos = (this.ball.x - this.paddle.x) / this.paddle.width;
					this.ball.dx = 5 * (hitPos - 0.5);
				}

				// 球与砖块碰撞检测
				this.checkBrickCollision();

				// 检查是否获胜
				this.checkWinCondition();
			},

			// 检查砖块碰撞
			checkBrickCollision() {
				for (let r = 0; r < this.brickRows; r++) {
					for (let c = 0; c < this.brickCols; c++) {
						const brick = this.bricks[r][c];
						if (brick.status === 1) {
							if (this.ball.x > brick.x &&
								this.ball.x < brick.x + this.brickWidth &&
								this.ball.y > brick.y &&
								this.ball.y < brick.y + this.brickHeight) {
								this.ball.dy = -this.ball.dy;
								brick.status = 0;
								this.score += 10;
							}
						}
					}
				}
			},

			// 检查获胜条件
			checkWinCondition() {
				let activeBricks = 0;
				for (let r = 0; r < this.brickRows; r++) {
					for (let c = 0; c < this.brickCols; c++) {
						if (this.bricks[r][c].status === 1) {
							activeBricks++;
						}
					}
				}

				if (activeBricks === 0) {
					this.gameWon = true;
					this.gameOver = true;
					this.gameRunning = false;
				}
			},

			// 绘制游戏
			drawGame() {
				if (!this.ctx) return;

				// 清空画布
				this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);

				// 绘制背景
				this.ctx.fillStyle = '#2C3E50';
				this.ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);

				// 绘制砖块
				this.drawBricks();

				// 绘制球
				this.drawBall();

				// 绘制挡板
				this.drawPaddle();
			},

			// 绘制砖块
			drawBricks() {
				for (let r = 0; r < this.brickRows; r++) {
					for (let c = 0; c < this.brickCols; c++) {
						const brick = this.bricks[r][c];
						if (brick.status === 1) {
							this.ctx.fillStyle = brick.color;
							this.ctx.fillRect(brick.x, brick.y, this.brickWidth, this.brickHeight);

							// 添加边框
							this.ctx.strokeStyle = '#FFFFFF';
							this.ctx.lineWidth = 1;
							this.ctx.strokeRect(brick.x, brick.y, this.brickWidth, this.brickHeight);
						}
					}
				}
			},

			// 绘制球
			drawBall() {
				this.ctx.beginPath();
				this.ctx.arc(this.ball.x, this.ball.y, this.ball.radius, 0, Math.PI * 2);
				this.ctx.fillStyle = '#E74C3C';
				this.ctx.fill();
				this.ctx.strokeStyle = '#FFFFFF';
				this.ctx.lineWidth = 2;
				this.ctx.stroke();
				this.ctx.closePath();
			},

			// 绘制挡板
			drawPaddle() {
				this.ctx.fillStyle = '#3498DB';
				this.ctx.fillRect(this.paddle.x, this.paddle.y, this.paddle.width, this.paddle.height);
				this.ctx.strokeStyle = '#FFFFFF';
				this.ctx.lineWidth = 2;
				this.ctx.strokeRect(this.paddle.x, this.paddle.y, this.paddle.width, this.paddle.height);
			},

			// 触摸开始
			handleTouchStart(e) {
				this.touchStartX = e.touches[0].clientX;
			},

			// 触摸移动
			handleTouchMove(e) {
				if (!this.gameRunning || this.gamePaused) return;

				const touchX = e.touches[0].clientX;
				const deltaX = touchX - this.touchStartX;

				// 移动挡板
				this.paddle.x += deltaX * 0.5;

				// 限制挡板在画布内
				if (this.paddle.x < 0) this.paddle.x = 0;
				if (this.paddle.x + this.paddle.width > this.canvasWidth) {
					this.paddle.x = this.canvasWidth - this.paddle.width;
				}

				this.touchStartX = touchX;
			}
		}
	}
</script>

<style>
.game-container {
	width: 100%;
	height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	box-sizing: border-box;
}

.game-header {
	width: 100%;
	max-width: 700rpx;
	margin-bottom: 30rpx;
	text-align: center;
}

.game-title {
	color: #FFFFFF;
	font-size: 48rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 20rpx;
	text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.game-info {
	display: flex;
	justify-content: space-between;
	padding: 0 40rpx;
}

.score, .lives {
	color: #FFFFFF;
	font-size: 32rpx;
	font-weight: bold;
	text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

.game-canvas {
	width: 700rpx;
	height: 1000rpx;
	background-color: #2C3E50;
	border: 4rpx solid #FFFFFF;
	border-radius: 10rpx;
	box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.3);
	margin-bottom: 30rpx;
}

.game-controls {
	display: flex;
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.btn {
	padding: 20rpx 40rpx;
	border-radius: 50rpx;
	font-size: 28rpx;
	font-weight: bold;
	border: none;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

.start-btn {
	background: linear-gradient(45deg, #4CAF50, #45a049);
	color: white;
}

.pause-btn {
	background: linear-gradient(45deg, #FF9800, #F57C00);
	color: white;
}

.reset-btn {
	background: linear-gradient(45deg, #F44336, #D32F2F);
	color: white;
}

.btn:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.3);
}

.game-over {
	text-align: center;
	background: rgba(0, 0, 0, 0.8);
	padding: 40rpx;
	border-radius: 20rpx;
	backdrop-filter: blur(10rpx);
}

.game-over-text {
	color: #FFFFFF;
	font-size: 40rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 20rpx;
}

.final-score {
	color: #FFD700;
	font-size: 32rpx;
	font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.game-canvas {
		width: 90vw;
		height: 60vh;
	}

	.game-title {
		font-size: 40rpx;
	}

	.score, .lives {
		font-size: 28rpx;
	}

	.btn {
		padding: 15rpx 30rpx;
		font-size: 24rpx;
	}
}
</style>
