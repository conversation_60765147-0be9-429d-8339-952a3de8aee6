<template>
	<view class="container">
		<!-- 装饰性圆形 -->
		<view class="decorative-circles">
			<view class="circle circle-blue-left-top"></view>
			<view class="circle circle-blue-left-bottom"></view>
			<view class="circle circle-blue-right"></view>
		</view>

		<!-- 主要内容 -->
		<view class="content">
			<!-- 标题 -->
			<view class="title-section">
				<text class="main-title">TukTuk（吐壳儿）— 让交互科技融入生活小时刻</text>
			</view>

			<!-- 公司介绍文字 -->
			<view class="description-section">
				<text class="description-text">
					TukTuk创立于2025年5月，位于河北沧州，是专注于科技化创意礼物的创新型品牌。我们通过智能技术与情感化设计的融合，为年轻消费者开发兼具趣味性与实用性的礼物产品，让科技成为传递心意的温暖载体。
				</text>

				<text class="description-text">
					以"创新、诚信、合作、卓越"为核心价值观，TukTuk致力于降低科技体验门槛，让一线至三四线城市的年轻群体均能享受科技赋予生活的惊喜感。未来，我们将持续探索技术与人情的共生关系，助力平凡日常绽放愉悦光芒。
				</text>
			</view>

			<!-- 中心Logo圆形 -->
			<view class="logo-circle">
				<text class="logo-text">TukTuk</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 简单的数据，不需要复杂的游戏逻辑
			}
		},

		mounted() {
			// 组件挂载完成
		},

		methods: {
			// 可以添加一些简单的交互方法
		}
	}
</script>

<style>
.container {
	width: 100%;
	height: 100vh;
	background: #d4532c;
	position: relative;
	overflow: hidden;
	padding: 60rpx;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
}

/* 装饰性圆形 */
.decorative-circles {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 1;
}

.circle {
	position: absolute;
	border-radius: 50%;
}

.circle-blue-left-top {
	width: 200rpx;
	height: 200rpx;
	background: #4A90E2;
	top: -100rpx;
	left: -100rpx;
}

.circle-blue-left-bottom {
	width: 300rpx;
	height: 300rpx;
	background: #4A90E2;
	bottom: -150rpx;
	left: -150rpx;
}

.circle-blue-right {
	width: 250rpx;
	height: 250rpx;
	background: #4A90E2;
	top: 50%;
	right: -125rpx;
	transform: translateY(-50%);
}

/* 主要内容 */
.content {
	position: relative;
	z-index: 10;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

/* 标题区域 */
.title-section {
	margin-bottom: 80rpx;
}

.main-title {
	font-size: 48rpx;
	font-weight: bold;
	color: white;
	line-height: 1.4;
	margin: 0;
	display: block;
}

/* 描述文字区域 */
.description-section {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 40rpx;
	margin-bottom: 100rpx;
}

.description-text {
	font-size: 32rpx;
	color: white;
	line-height: 1.6;
	margin: 0;
	display: block;
}

/* 中心Logo圆形 */
.logo-circle {
	position: absolute;
	bottom: 200rpx;
	left: 50%;
	transform: translateX(-50%);
	width: 400rpx;
	height: 400rpx;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 10;
}

.logo-text {
	font-size: 60rpx;
	font-weight: bold;
	color: #d4532c;
	letter-spacing: 4rpx;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.container {
		padding: 40rpx;
	}

	.main-title {
		font-size: 36rpx;
	}

	.description-text {
		font-size: 28rpx;
	}

	.logo-circle {
		width: 300rpx;
		height: 300rpx;
		bottom: 150rpx;
	}

	.logo-text {
		font-size: 45rpx;
	}

	.circle-blue-left-top {
		width: 150rpx;
		height: 150rpx;
		top: -75rpx;
		left: -75rpx;
	}

	.circle-blue-left-bottom {
		width: 200rpx;
		height: 200rpx;
		bottom: -100rpx;
		left: -100rpx;
	}

	.circle-blue-right {
		width: 180rpx;
		height: 180rpx;
		right: -90rpx;
	}
}


</style>
