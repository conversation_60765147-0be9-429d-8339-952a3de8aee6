<template>
	<view class="brick-breaker-container">
		<!-- 装饰性圆点 -->
		<view class="decorative-dots">
			<view class="dot dot-orange-large" style="top: 2%; left: 2%;"></view>
			<view class="dot dot-yellow-medium" style="top: 5%; left: 25%;"></view>
			<view class="dot dot-blue-small" style="top: 8%; right: 30%;"></view>
			<view class="dot dot-orange-medium" style="top: 15%; right: 5%;"></view>
			<view class="dot dot-pink-small" style="top: 25%; left: 8%;"></view>
			<view class="dot dot-blue-medium" style="bottom: 20%; right: 15%;"></view>
			<view class="dot dot-yellow-small" style="bottom: 15%; left: 20%;"></view>
			<view class="dot dot-orange-small" style="bottom: 8%; right: 25%;"></view>
			<view class="dot dot-pink-medium" style="bottom: 5%; left: 40%;"></view>
		</view>

		<!-- 左上角 Logo 区域 -->
		<view class="logo-section">
			<view class="logo-container">
				<text class="logo-number">12</text>
				<text class="logo-text">WAVE</text>
				<view class="logo-subtitle">
					<text>INTERACTIVE STUDIO</text>
				</view>
			</view>
		</view>

		<!-- 右上角控制按钮 -->
		<view class="top-controls">
			<button class="control-icon sound-btn">🔊</button>
			<button class="control-icon menu-btn">☰</button>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content">
			<view class="content-header">
				<text class="main-title">TukTuk（吐壳儿）</text>
				<text class="sub-title">让交互科技融入生活小时刻</text>
			</view>

			<!-- 游戏区域 -->
			<view class="game-area">
				<canvas
					canvas-id="gameCanvas"
					:style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"
					@touchmove="movePaddle"
				></canvas>

				<view v-if="!gameStarted" class="start-overlay">
					<view class="start-message">
						<view class="play-button-container">
							<button @click="startGame" class="modern-play-btn">
								<view class="play-icon">
									<text>▶</text>
								</view>
							</button>
							<view class="play-text">
								<text>Play...</text>
							</view>
						</view>
					</view>
				</view>

				<view v-if="gameOver" class="game-over-overlay">
					<view class="game-over-message">
						<text class="game-over-title">Game Over</text>
						<text class="game-over-score">Words touched: {{ touchedWords }}</text>
						<button @click="resetGame" class="restart-btn">Play Again</button>
					</view>
				</view>
			</view>

			<!-- 游戏统计 -->
			<view class="game-stats">
				<view class="stat-item">
					<text class="stat-label">Score</text>
					<text class="stat-value">{{ touchedWords }}</text>
				</view>
				<view class="stat-item">
					<text class="stat-label">Lives</text>
					<text class="stat-value">{{ lives }}</text>
				</view>
			</view>
		</view>

		<!-- 底部信息 -->
		<view class="bottom-section">
			<view class="copyright">
				<text>© 2025 TukTuk（吐壳儿）版权所有</text>
			</view>
			<view class="social-links">
				<text class="social-link">✕</text>
				<text class="social-link">f</text>
				<text class="email-link">联系我们！</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// Canvas 设置 - 响应式初始化
				canvasWidth: (() => {
					const isMobile = uni.getSystemInfoSync().windowWidth <= 768
					const padding = isMobile ? 40 : 100
					const maxWidth = isMobile ? 800 : 1000
					return Math.min(maxWidth, uni.getSystemInfoSync().windowWidth - padding)
				})(),
				canvasHeight: (() => {
					const isMobile = uni.getSystemInfoSync().windowWidth <= 768
					const maxHeight = isMobile ? 500 : 700
					return Math.min(maxHeight, uni.getSystemInfoSync().windowHeight - (isMobile ? 350 : 200))
				})(),
				ctx: null,

				// 游戏状态
				gameStarted: false,
				gameOver: false,
				paused: false,
				touchedWords: 0,
				lives: 3,

				// 游戏对象
				ball: {
					x: 400,
					y: (() => {
						const isMobile = uni.getSystemInfoSync().windowWidth <= 768
						const maxHeight = isMobile ? 500 : 700
						const canvasHeight = Math.min(maxHeight, uni.getSystemInfoSync().windowHeight - (isMobile ? 300 : 200))
						return canvasHeight - 80 // 球在挡板上方50像素
					})(),
					dx: 4,
					dy: -4,
					radius: 8
				},

				paddle: {
					x: 350,
					y: (() => {
						const isMobile = uni.getSystemInfoSync().windowWidth <= 768
						const maxHeight = isMobile ? 500 : 700
						const canvasHeight = Math.min(maxHeight, uni.getSystemInfoSync().windowHeight - (isMobile ? 300 : 200))
						return canvasHeight - 30 // 挡板距离底部30像素
					})(),
					width: 100,
					height: 15
				},

				words: [],

				// 文字设置
				companyText: [
					'TukTuk（吐壳儿）创立于2025年5月，总部位于河北沧州，是专注于科技化创意礼物的创新型品牌。',
					'我们通过智能技术与情感化设计的融合，为年轻消费者开发兼具趣味性与实用性的礼物产品，',
					'让科技成为传递心意的温暖载体。以"创新、诚信、合作、卓越"为核心价值观，',
					'TukTuk致力于降低科技体验门槛，让一线至三四线城市的年轻群体均能享受科技赋予生活的惊喜感。',
					'未来，我们将持续探索技术与人情的共生关系，助力平凡日常绽放愉悦光芒。'
				],

				// 动画
				animationId: null,

				// 手绘效果
				wobbleOffset: 0
			}
		},

		mounted() {
			this.updateCanvasSize()
			this.initGame()
		},

		beforeUnmount() {
			if (this.animationId) {
				cancelAnimationFrame(this.animationId)
			}
		},

		methods: {
			updateCanvasSize() {
				// 响应式canvas尺寸计算
				const systemInfo = uni.getSystemInfoSync()
				const isMobile = systemInfo.windowWidth <= 768
				const padding = isMobile ? 40 : 100
				const maxWidth = isMobile ? 800 : 800 // PC端也限制为800，确保文字排列一致
				const maxHeight = isMobile ? 500 : 700

				this.canvasWidth = Math.min(maxWidth, systemInfo.windowWidth - padding)
				this.canvasHeight = Math.min(maxHeight, systemInfo.windowHeight - (isMobile ? 350 : 200))

				// 如果canvas已经存在，重新初始化游戏元素
				if (this.ctx) {
					// 更新挡板位置
					this.paddle.y = this.canvasHeight - 30
					this.paddle.x = (this.canvasWidth - this.paddle.width) / 2
					this.resetBall()
					this.createWords()
					this.draw()
				}
			},

			initGame() {
				const query = uni.createSelectorQuery().in(this)
				query.select('#gameCanvas').fields({ node: true, size: true }).exec((res) => {
					if (res[0]) {
						const canvas = res[0].node
						this.ctx = canvas.getContext('2d')

						// 设置画布尺寸
						canvas.width = this.canvasWidth
						canvas.height = this.canvasHeight

						this.createWords()
						this.draw()

						// 开始文字动画循环（即使游戏未开始也要显示文字动画）
						this.startTextAnimation()
					}
				})
			},

			startTextAnimation() {
				// 文字现在是静态的，只需要绘制一次
				if (!this.gameStarted) {
					this.draw()
				}
			},

			createWords() {
				this.words = []
				const systemInfo = uni.getSystemInfoSync()
				const isMobile = systemInfo.windowWidth <= 768
				let x = 0 // 从canvas左边缘开始
				let y = isMobile ? 25 : 30 // 手机版起始位置更靠上
				const lineHeight = isMobile ? 28 : 50 // 手机版使用更小的行高
				const maxWidth = this.canvasWidth // 使用完整的canvas宽度

				// 检查context是否可用
				if (!this.ctx) {
					console.warn('Canvas context not available for createWords')
					return
				}

				// 将所有句子合并并按字符分割
				const allText = this.companyText.join('')
				const characters = allText.split('')

				characters.forEach((char) => {
					// 跳过空格和换行符
					if (char === ' ' || char === '\n') {
						const spacing = isMobile ? 6 : 12
						x += spacing
						return
					}

					// 响应式字体大小
					const fontSize = isMobile ? 16 : 28 // 手机版使用16px，PC版使用28px
					const fontWeight = 'normal'

					// 设置字体并测量文字宽度
					this.ctx.font = `${fontWeight} ${fontSize}px "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", sans-serif`
					const textWidth = this.ctx.measureText(char).width // 文字本身的宽度
					const spacing = isMobile ? 6 : 12 // 中文字符间距更小
					const charWidth = textWidth + spacing // 总宽度（文字+间距）

					// 检查是否需要换行
					if (x + textWidth >= maxWidth) {
						x = 0 // 从左边缘开始新行
						y += lineHeight
					}

					this.words.push({
						text: char,
						x: x,
						y: y,
						width: textWidth, // 使用文字本身的宽度
						height: fontSize + 5,
						fontSize: fontSize,
						fontWeight: fontWeight,
						color: '#2c3e50',
						touched: false
					})

					x += charWidth // 移动位置时使用包含间距的宽度
				})
			},

			startGame() {
				console.log('Play button clicked!', { gameStarted: this.gameStarted, gameOver: this.gameOver })

				// 确保canvas已经初始化
				if (!this.ctx) {
					console.warn('Canvas not ready, initializing...')
					this.initGame()
					if (!this.ctx) {
						console.error('Failed to initialize canvas')
						return
					}
				}

				if (!this.gameStarted && !this.gameOver) {
					this.gameStarted = true
					// 重置球的位置到中心
					this.ball.x = this.canvasWidth / 2
					this.ball.y = this.paddle.y - 50 // 球在挡板上方50像素
					this.ball.dx = 4
					this.ball.dy = -4
					// 重置挡板位置
					this.paddle.x = (this.canvasWidth - this.paddle.width) / 2
					console.log('Game started! Ball position:', this.ball)
					this.gameLoop()
				}
			},

			gameLoop() {
				if (!this.paused && !this.gameOver && this.ctx) {
					this.update()
					this.draw()
					this.animationId = requestAnimationFrame(this.gameLoop)
				}
			},

			update() {
				// 更新球的位置
				this.ball.x += this.ball.dx
				this.ball.y += this.ball.dy

				// 球与墙壁碰撞 - 确保球严格在canvas内
				if (this.ball.x + this.ball.radius >= this.canvasWidth || this.ball.x - this.ball.radius <= 0) {
					this.ball.dx = -this.ball.dx
				}

				if (this.ball.y - this.ball.radius < 0) {
					this.ball.dy = -this.ball.dy
				}

				// 球掉落 - 如果球跑到挡板下面就死亡
				if (this.ball.y + this.ball.radius > this.paddle.y + this.paddle.height) {
					this.lives--
					if (this.lives <= 0) {
						this.gameOver = true
						this.won = false
					} else {
						this.resetBall()
					}
				}

				// 球与挡板碰撞
				if (this.ball.y + this.ball.radius > this.paddle.y &&
					this.ball.x > this.paddle.x &&
					this.ball.x < this.paddle.x + this.paddle.width) {
					// 根据击中位置改变角度
					const hitPos = (this.ball.x - this.paddle.x) / this.paddle.width
					const angle = (hitPos - 0.5) * Math.PI / 3
					const speed = Math.sqrt(this.ball.dx * this.ball.dx + this.ball.dy * this.ball.dy)
					this.ball.dx = Math.sin(angle) * speed
					this.ball.dy = -Math.abs(Math.cos(angle) * speed)
				}

				// 球与文字碰撞
				for (let word of this.words) {
					if (!word.touched && this.ballWordCollision(word)) {
						this.ball.dy = -this.ball.dy
						word.touched = true
						word.color = '#e9554d'
						this.touchedWords += 1
						break
					}
				}

				// 更新手绘效果
				this.wobbleOffset += 0.1
			},

			draw() {
				if (!this.ctx) {
					console.warn('Canvas context not available')
					return
				}

				// 清空画布
				this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)

				// 绘制背景
				this.drawBackground()

				// 绘制文字
				this.drawWords()

				// 绘制挡板
				this.drawPaddle()

				// 绘制球
				this.drawBall()
			},

			drawBackground() {
				// 背景现在是透明的，不绘制任何边框
			},

			drawWords() {
				for (let word of this.words) {
					this.ctx.save()

					// 设置文字位置（移除摆动效果）
					this.ctx.translate(word.x, word.y)

					// 设置字体
					this.ctx.font = `${word.fontWeight} ${word.fontSize}px Inter, sans-serif`
					this.ctx.fillStyle = word.color
					this.ctx.textAlign = 'left'
					this.ctx.textBaseline = 'top'

					// 绘制文字
					this.ctx.fillText(word.text, 0, 0)

					// 如果被触碰，添加发光效果
					if (word.touched) {
						this.ctx.shadowColor = '#e9554d'
						this.ctx.shadowBlur = 8
						this.ctx.shadowOffsetX = 0
						this.ctx.shadowOffsetY = 0
						this.ctx.fillText(word.text, 0, 0)
						this.ctx.shadowBlur = 0
					}

					this.ctx.restore()
				}
			},

			drawPaddle() {
				this.ctx.save()

				// 手绘风格的挡板
				const wobble = Math.sin(this.wobbleOffset * 2) * 1
				this.ctx.translate(this.paddle.x + wobble, this.paddle.y)
				this.ctx.rotate(Math.sin(this.wobbleOffset * 0.3) * 0.02)

				// 挡板主体 - 现代渐变风格
				const gradient = this.ctx.createLinearGradient(0, 0, 0, this.paddle.height)
				gradient.addColorStop(0, '#667eea')
				gradient.addColorStop(1, '#764ba2')
				this.ctx.fillStyle = gradient
				this.ctx.fillRect(0, 0, this.paddle.width, this.paddle.height)

				// 现代边框
				this.ctx.strokeStyle = 'rgba(44, 62, 80, 0.3)'
				this.ctx.lineWidth = 2
				this.ctx.strokeRect(0, 0, this.paddle.width, this.paddle.height)

				// 高光效果
				this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)'
				this.ctx.fillRect(2, 2, this.paddle.width - 4, 4)

				this.ctx.restore()
			},

			drawBall() {
				this.ctx.save()

				// 手绘风格的球
				const wobble = Math.sin(this.wobbleOffset * 3) * 0.5
				this.ctx.translate(this.ball.x + wobble, this.ball.y + wobble)

				// 球的主体 - 现代渐变风格
				const ballGradient = this.ctx.createRadialGradient(-2, -2, 0, 0, 0, this.ball.radius)
				ballGradient.addColorStop(0, '#667eea')
				ballGradient.addColorStop(0.7, '#764ba2')
				ballGradient.addColorStop(1, '#2c3e50')

				this.ctx.fillStyle = ballGradient
				this.ctx.beginPath()
				this.ctx.arc(0, 0, this.ball.radius, 0, Math.PI * 2)
				this.ctx.fill()

				// 球的边框
				this.ctx.strokeStyle = 'rgba(44, 62, 80, 0.4)'
				this.ctx.lineWidth = 1.5
				this.ctx.stroke()

				// 高光
				this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'
				this.ctx.beginPath()
				this.ctx.arc(-2, -2, 2.5, 0, Math.PI * 2)
				this.ctx.fill()

				this.ctx.restore()
			},

			ballWordCollision(word) {
				return this.ball.x + this.ball.radius > word.x &&
					   this.ball.x - this.ball.radius < word.x + word.width &&
					   this.ball.y + this.ball.radius > word.y &&
					   this.ball.y - this.ball.radius < word.y + word.height
			},

			movePaddle(event) {
				if (this.gameStarted && !this.gameOver && !this.paused) {
					// 获取触摸位置
					const touch = event.touches[0]
					if (touch) {
						// 计算触摸在canvas内的相对位置
						const query = uni.createSelectorQuery().in(this)
						query.select('#gameCanvas').boundingClientRect().exec((res) => {
							if (res[0]) {
								const rect = res[0]
								const scaleX = this.canvasWidth / rect.width
								const touchX = (touch.clientX - rect.left) * scaleX

								// 挡板移动范围应该与文字模块宽度一致（从0到canvasWidth）
								const minX = 0
								const maxX = this.canvasWidth - this.paddle.width
								this.paddle.x = Math.max(minX, Math.min(touchX - this.paddle.width / 2, maxX))
							}
						})
					}
				}
			},

			resetBall() {
				this.ball.x = this.canvasWidth / 2
				this.ball.y = this.paddle.y - 50 // 球在挡板上方50像素
				this.ball.dx = (Math.random() - 0.5) * 8
				this.ball.dy = -4
			},

			pauseGame() {
				this.paused = !this.paused
				if (!this.paused) {
					this.gameLoop()
				}
			},

			resetGame() {
				this.gameStarted = false
				this.gameOver = false
				this.paused = false
				this.touchedWords = 0
				this.lives = 3

				this.resetBall()
				this.paddle.x = (this.canvasWidth - this.paddle.width) / 2

				// 确保canvas已经初始化再创建文字和绘制
				if (this.ctx) {
					this.createWords()
					this.draw()
					// 重新开始文字动画
					this.startTextAnimation()
				} else {
					this.initGame()
				}

				if (this.animationId) {
					cancelAnimationFrame(this.animationId)
				}
			}
		}
	}
</script>

<style>
.brick-breaker-container {
	width: 100%;
	height: 100vh;
	background: url('/static/bg.jpg') center center / cover no-repeat;
	position: relative;
	overflow: hidden;
	font-family: 'Arial', sans-serif;
	display: flex;
	flex-direction: column;
}

/* 装饰性圆点 */
.decorative-dots {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 1;
}

.dot {
	position: absolute;
	border-radius: 50%;
	opacity: 0.8;
}

.dot-orange-large { width: 120rpx; height: 120rpx; background: #ff6b47; }
.dot-orange-medium { width: 80rpx; height: 80rpx; background: #ff8566; }
.dot-orange-small { width: 40rpx; height: 40rpx; background: #ff9980; }
.dot-yellow-medium { width: 70rpx; height: 70rpx; background: #ffd93d; }
.dot-yellow-small { width: 30rpx; height: 30rpx; background: #ffe066; }
.dot-blue-medium { width: 90rpx; height: 90rpx; background: #4a90e2; }
.dot-blue-small { width: 50rpx; height: 50rpx; background: #6ba3f0; }
.dot-pink-medium { width: 60rpx; height: 60rpx; background: #ff6b9d; }
.dot-pink-small { width: 36rpx; height: 36rpx; background: #ff85b3; }

/* Logo 区域 */
.logo-section {
	position: absolute;
	top: 60rpx;
	left: 60rpx;
	z-index: 10;
}

.logo-container {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
}

.logo-number {
	font-size: 60rpx;
	font-weight: bold;
	color: #ff6b47;
	line-height: 0.8;
}

.logo-text {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	letter-spacing: 4rpx;
	margin-top: -10rpx;
}

.logo-subtitle text {
	font-size: 14rpx;
	color: #666;
	letter-spacing: 2rpx;
	margin-top: 4rpx;
}

/* 顶部控制按钮 */
.top-controls {
	position: absolute;
	top: 60rpx;
	right: 60rpx;
	display: flex;
	gap: 30rpx;
	z-index: 10;
}

.control-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	background: #333;
	color: white;
	border: none;
	font-size: 24rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
}

.control-icon:hover {
	transform: scale(1.1);
	background: #555;
}

/* 主要内容区域 */
.main-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	z-index: 5;
	padding: 0 100rpx;
	width: 100%;
	max-width: 1600rpx;
	margin: 0 auto;
}

.content-header {
	text-align: center;
	margin-bottom: 80rpx;
}

.main-title {
	font-size: 80rpx;
	font-weight: bold;
	color: #333;
	margin: 0 0 20rpx 0;
	letter-spacing: -4rpx;
}

.sub-title {
	font-size: 30rpx;
	color: #666;
	margin: 0;
	font-weight: normal;
}

.game-area {
	position: relative;
	border: none;
	border-radius: 0;
	background: transparent;
	box-shadow: none;
	overflow: hidden;
	backdrop-filter: none;
	margin-bottom: 60rpx;
	width: auto;
	max-width: none;
	display: flex;
	justify-content: center;
}

/* 游戏统计样式 */
.game-stats {
	display: flex;
	gap: 60rpx;
	justify-content: center;
	margin-bottom: 40rpx;
}

.stat-item {
	text-align: center;
}

.stat-label {
	display: block;
	font-size: 18rpx;
	color: #666;
	margin-bottom: 10rpx;
	text-transform: uppercase;
	letter-spacing: 2rpx;
}

.stat-value {
	display: block;
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
}

canvas {
	display: block;
	cursor: none;
}

.start-overlay,
.game-over-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: transparent;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #333;
	text-align: center;
	backdrop-filter: none;
}

.start-message .game-over-title,
.game-over-message .game-over-title {
	font-size: 44rpx;
	margin-bottom: 40rpx;
	color: #333;
	font-weight: 700;
	letter-spacing: -0.04rpx;
}

.start-message text {
	font-size: 22rpx;
	margin: 16rpx 0;
	color: #666;
	font-weight: 400;
}

.game-over-message .game-over-score {
	color: #666;
}

/* 现代播放按钮样式 */
.play-button-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 30rpx;
}

.modern-play-btn {
	width: 160rpx;
	height: 160rpx;
	border-radius: 50%;
	background: #333;
	border: none;
	cursor: pointer;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
}

.modern-play-btn:hover {
	transform: scale(1.1);
	background: #555;
}

.modern-play-btn .play-icon text {
	color: white;
	font-size: 40rpx;
	margin-left: 10rpx;
}

.play-text text {
	font-size: 24rpx;
	color: #333;
	font-weight: 500;
}

.restart-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border: none;
	border-radius: 50rpx;
	padding: 24rpx 50rpx;
	font-size: 22rpx;
	font-weight: 600;
	color: white;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.3);
	font-family: 'Inter', sans-serif;
}

.restart-btn:hover {
	transform: translateY(-4rpx);
	background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
	box-shadow: 0 16rpx 50rpx rgba(102, 126, 234, 0.4);
}

/* 底部区域 */
.bottom-section {
	position: absolute;
	bottom: 60rpx;
	left: 0;
	right: 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 100rpx;
	z-index: 10;
}

.copyright text {
	font-size: 16rpx;
	color: #666;
}

.social-links {
	display: flex;
	gap: 30rpx;
	align-items: center;
}

.social-link {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: #333;
	color: white;
	text-decoration: none;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	transition: all 0.3s ease;
}

.social-link:hover {
	transform: scale(1.1);
	background: #555;
}

.email-link {
	background: #4CAF50;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 50rpx;
	text-decoration: none;
	font-size: 18rpx;
	font-weight: 500;
	transition: all 0.3s ease;
}

.email-link:hover {
	background: #45a049;
	transform: translateY(-4rpx);
}

/* 手机版响应式设计 */
@media (max-width: 768px) {
	.brick-breaker-container {
		padding: 0;
	}

	/* Logo 区域 - 手机版 */
	.logo-section {
		top: 40rpx;
		left: 40rpx;
	}

	.logo-number {
		font-size: 40rpx;
	}

	.logo-text {
		font-size: 24rpx;
	}

	.logo-subtitle text {
		font-size: 12rpx;
	}

	/* 顶部控制按钮 - 手机版 */
	.top-controls {
		top: 40rpx;
		right: 40rpx;
		gap: 20rpx;
	}

	.control-icon {
		width: 80rpx;
		height: 80rpx;
		font-size: 20rpx;
	}

	/* 主要内容区域 - 手机版 */
	.main-content {
		padding: 0 40rpx;
		margin-top: 160rpx;
	}

	.content-header {
		margin-bottom: 60rpx;
	}

	.main-title {
		font-size: 50rpx;
		letter-spacing: -2rpx;
	}

	.sub-title {
		font-size: 24rpx;
	}

	/* 游戏区域 - 手机版 */
	.game-area {
		width: auto;
		max-width: none;
		margin-bottom: 40rpx;
		justify-content: center;
	}

	/* 游戏统计 - 手机版 */
	.game-stats {
		gap: 40rpx;
		margin-bottom: 60rpx;
		margin-top: 40rpx;
	}

	.stat-label {
		font-size: 16rpx;
	}

	.stat-value {
		font-size: 30rpx;
	}

	/* 播放按钮 - 手机版 */
	.modern-play-btn {
		width: 120rpx;
		height: 120rpx;
	}

	.modern-play-btn .play-icon text {
		font-size: 30rpx;
	}

	.play-text text {
		font-size: 20rpx;
	}

	/* 底部区域 - 手机版 */
	.bottom-section {
		bottom: 30rpx;
		padding: 0 40rpx;
		flex-direction: column;
		gap: 40rpx;
		text-align: center;
	}

	.copyright text {
		font-size: 14rpx;
	}

	.social-links {
		gap: 20rpx;
	}

	.social-link {
		width: 70rpx;
		height: 70rpx;
		font-size: 18rpx;
	}

	.email-link {
		padding: 16rpx 32rpx;
		font-size: 16rpx;
	}

	/* 装饰圆点 - 手机版调整 */
	.dot-orange-large { width: 80rpx; height: 80rpx; }
	.dot-orange-medium { width: 60rpx; height: 60rpx; }
	.dot-blue-medium { width: 70rpx; height: 70rpx; }
	.dot-yellow-medium { width: 50rpx; height: 50rpx; }
	.dot-pink-medium { width: 40rpx; height: 40rpx; }
}

/* 小屏手机版 */
@media (max-width: 480px) {
	.main-title {
		font-size: 40rpx;
	}

	.sub-title {
		font-size: 20rpx;
	}

	.logo-number {
		font-size: 30rpx;
	}

	.logo-text {
		font-size: 20rpx;
	}

	.main-content {
		padding: 0 30rpx;
		margin-top: 140rpx;
	}

	.bottom-section {
		padding: 0 30rpx;
	}

	.game-stats {
		gap: 30rpx;
		margin-bottom: 50rpx;
	}

	.stat-value {
		font-size: 24rpx;
	}
}
</style>
