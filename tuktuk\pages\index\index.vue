<template>
	<view class="container">
		<view class="loading-circle">
			<!-- 圆形文字 -->
			<view class="circle-text">
				<text
					v-for="(char, index) in textArray"
					:key="index"
					class="char"
					:style="getCharStyle(index)"
				>
					{{ char }}
				</text>
			</view>

			<!-- 中间百分比 -->
			<view class="center-percentage">
				<text class="percentage-text">{{ percentage }}%</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				text: '正在为你筹备美好时光',
				percentage: 0,
				timer: null
			}
		},
		computed: {
			textArray() {
				return this.text.split('');
			}
		},
		onLoad() {
			this.startLoading();
		},
		onUnload() {
			if (this.timer) {
				clearInterval(this.timer);
			}
		},
		methods: {
			startLoading() {
				this.timer = setInterval(() => {
					if (this.percentage < 100) {
						this.percentage += 1;
					} else {
						clearInterval(this.timer);
						// 加载完成后的操作
						this.onLoadComplete();
					}
				}, 50); // 5秒内完成加载
			},

			getCharStyle(index) {
				const totalChars = this.textArray.length;
				const angle = (360 / totalChars) * index;
				const radius = 120; // 圆的半径

				return {
					transform: `rotate(${angle}deg) translateY(-${radius}rpx) rotate(-${angle}deg)`,
					position: 'absolute',
					left: '50%',
					top: '50%',
					transformOrigin: '0 0'
				};
			},

			onLoadComplete() {
				// 加载完成后可以跳转到其他页面或执行其他操作
				console.log('加载完成');
			}
		}
	}
</script>

<style>
	.container {
		width: 100vw;
		height: 100vh;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	.loading-circle {
		position: relative;
		width: 300rpx;
		height: 300rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.circle-text {
		position: relative;
		width: 100%;
		height: 100%;
	}

	.char {
		color: #ffffff;
		font-size: 28rpx;
		font-weight: 500;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
		animation: glow 2s ease-in-out infinite alternate;
	}

	.center-percentage {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 10;
	}

	.percentage-text {
		color: #ffffff;
		font-size: 48rpx;
		font-weight: bold;
		text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
		animation: pulse 1s ease-in-out infinite;
	}

	@keyframes glow {
		0% {
			text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
		}
		100% {
			text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3), 0 0 20rpx rgba(255, 255, 255, 0.5);
		}
	}

	@keyframes pulse {
		0%, 100% {
			transform: translate(-50%, -50%) scale(1);
		}
		50% {
			transform: translate(-50%, -50%) scale(1.1);
		}
	}
</style>
