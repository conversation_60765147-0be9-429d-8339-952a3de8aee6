<template>
	<view class="container" @click="handlePageClick">
		<view class="loading-circle">
			<!-- 圆形文字 -->
			<view class="circle-text">
				<view
					v-for="(char, index) in textArray"
					:key="index"
					class="char-wrapper"
					:style="getCharWrapperStyle(index)"
				>
					<text class="char">{{ char }}</text>
				</view>
			</view>

			<!-- 中间百分比 -->
			<view class="center-percentage">
				<text class="percentage-text">{{ percentage }}%</text>
			</view>
		</view>

		<!-- 加载完成提示 -->
		<view v-if="isLoadComplete" class="click-hint">
			<text class="hint-text">点击屏幕继续</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				text: '正在为你筹备美好时光',
				percentage: 0,
				timer: null,
				isLoadComplete: false
			}
		},
		computed: {
			textArray() {
				return this.text.split('');
			}
		},
		onLoad() {
			this.startLoading();
		},
		onUnload() {
			if (this.timer) {
				clearInterval(this.timer);
			}
		},
		methods: {
			startLoading() {
				this.timer = setInterval(() => {
					if (this.percentage < 100) {
						this.percentage += 1;
					} else {
						clearInterval(this.timer);
						// 加载完成后的操作
						this.onLoadComplete();
					}
				}, 50); // 5秒内完成加载
			},

			getCharWrapperStyle(index) {
				const totalChars = this.textArray.length;
				const angle = (360 / totalChars) * index - 90; // -90度让第一个字符在顶部

				return {
					transform: `rotate(${angle}deg)`
				};
			},

			onLoadComplete() {
				// 加载完成后显示点击提示
				this.isLoadComplete = true;
				console.log('加载完成');
			},

			handlePageClick() {
				// 只有在加载完成后才能点击跳转
				if (this.isLoadComplete) {
					console.log('准备跳转到second页面');
					uni.redirectTo({
						url: '/pages/second/second',
						success: () => {
							console.log('跳转成功');
						},
						fail: (err) => {
							console.error('跳转失败:', err);
							// 如果redirectTo也失败，可能是路径问题
							console.log('尝试使用相对路径跳转');
							uni.redirectTo({
								url: '../second/second',
								fail: (err2) => {
									console.error('相对路径跳转也失败:', err2);
								}
							});
						}
					});
				}
			}
		}
	}
</script>

<style>
	@font-face {
		font-family: 'DemoSlantBlack';
		src: url('/static/演示斜黑体.otf') format('opentype');
		font-weight: normal;
		font-style: normal;
	}

	.container {
		width: 100vw;
		height: 100vh;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #7bb3d3;
	}

	.loading-circle {
		position: relative;
		width: 600rpx;
		height: 600rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.circle-text {
		position: relative;
		width: 100%;
		height: 100%;
		animation: rotate 20s linear infinite;
	}

	.char-wrapper {
		position: absolute;
		left: 50%;
		top: 50%;
		width: 60rpx;
		height: 300rpx;
		margin-left: -30rpx;
		margin-top: -300rpx;
		transform-origin: 30rpx 300rpx;
	}

	.char {
		color: #ffffff;
		font-size: 80rpx;
		font-family: 'DemoSlantBlack', sans-serif;
		font-weight: 500;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
		animation: glow 2s ease-in-out infinite alternate;
		display: block;
		text-align: center;
	}

	.center-percentage {
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		z-index: 10;
	}

	.percentage-text {
		color: #ffffff;
		font-size: 120rpx;
		font-family: 'DemoSlantBlack', sans-serif;
		font-weight: bold;
		text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
		animation: pulse 1s ease-in-out infinite;
	}

	@keyframes glow {
		0% {
			text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
		}
		100% {
			text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3), 0 0 20rpx rgba(255, 255, 255, 0.5);
		}
	}

	@keyframes pulse {
		0%, 100% {
			transform: translate(-50%, -50%) scale(1);
		}
		50% {
			transform: translate(-50%, -50%) scale(1.1);
		}
	}

	@keyframes rotate {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

	.click-hint {
		position: absolute;
		bottom: 100rpx;
		left: 50%;
		transform: translateX(-50%);
		animation: fadeInOut 2s ease-in-out infinite;
	}

	.hint-text {
		color: #ffffff;
		font-size: 32rpx;
		font-family: 'DemoSlantBlack', sans-serif;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
	}

	@keyframes fadeInOut {
		0%, 100% {
			opacity: 0.5;
		}
		50% {
			opacity: 1;
		}
	}
</style>
