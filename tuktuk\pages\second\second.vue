<template>
	<view class="container">
		<view class="content">
			<text class="title">欢迎来到第二页！</text>
			<text class="subtitle">跳转成功了！</text>
			<view class="back-btn" @click="goBack">
				<text class="btn-text">返回首页</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		methods: {
			goBack() {
				window.location.href = window.location.href.replace('/pages/second/second', '/pages/index/index');
			}
		}
	}
</script>

<style>
	.container {
		width: 100vw;
		height: 100vh;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #ff6b6b;
	}

	.content {
		text-align: center;
	}

	.title {
		color: #ffffff;
		font-size: 48rpx;
		font-weight: bold;
		display: block;
		margin-bottom: 40rpx;
	}

	.subtitle {
		color: #ffffff;
		font-size: 32rpx;
		display: block;
		margin-bottom: 80rpx;
	}

	.back-btn {
		background-color: #ffffff;
		padding: 20rpx 40rpx;
		border-radius: 50rpx;
		display: inline-block;
	}

	.btn-text {
		color: #ff6b6b;
		font-size: 28rpx;
		font-weight: bold;
	}
</style>
