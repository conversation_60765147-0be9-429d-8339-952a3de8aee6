<template>
	<view class="second-container"
		@touchstart="handleTouchStart"
		@touchmove="handleTouchMove"
		@touchend="handleTouchEnd">

		<!-- 顶部Logo -->
		<view class="logo-container">
			<image src="/static/logo.png" class="logo-image" mode="widthFix" />
		</view>

		<!-- 飘动图片 -->
		<view class="floating-images">
			<image src="/static/1.png" class="float-img float-img-1" mode="aspectFit" />
			<image src="/static/2.png" class="float-img float-img-2" mode="aspectFit" />
			<image src="/static/3.png" class="float-img float-img-3" mode="aspectFit" />
			<image src="/static/4.png" class="float-img float-img-4" mode="aspectFit" />
			<image src="/static/5.png" class="float-img float-img-5" mode="aspectFit" />
			<image src="/static/6.png" class="float-img float-img-6" mode="aspectFit" />
			<image src="/static/7.png" class="float-img float-img-7" mode="aspectFit" />
			<image src="/static/8.png" class="float-img float-img-8" mode="aspectFit" />
		</view>

		<!-- 时钟图片 -->
		<view class="clock-container" :class="{ 'animate-in': showClock }">
			<image :src="clockImage" class="clock-image" mode="aspectFit" @click="switchClock" />
			<!-- 闪动文字 -->
			<view class="clock-text">
				<text>拨动指针到你心里的位置</text>
			</view>
			<!-- 下滑提示 -->
			<view class="slide-hint" :class="{ 'show': showSlideHint }">
				<view class="slide-arrow">
					<text>↓</text>
				</view>
				<view class="slide-text">
					<text>向下滑动</text>
				</view>
			</view>
		</view>

		<!-- 底部图片 -->
		<view class="bottom-image">
			<image src="/static/end-logo.png" class="end-logo" mode="widthFix" />
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				showClock: false,
				clockImage: '/static/clock-one.png',
				showSlideHint: false,
				touchStartY: 0,
				touchEndY: 0,
				isSliding: false
			}
		},
		mounted() {
			// 页面加载后延迟触发动画
			setTimeout(() => {
				this.showClock = true
			}, 300)

			// 2秒后显示下滑提示（去掉自动切换时钟图片）
			setTimeout(() => {
				this.showSlideHint = true
			}, 2000)
		},
		methods: {
			// 触摸开始
			handleTouchStart(event) {
				this.touchStartY = event.touches[0].clientY
				this.isSliding = false
			},

			// 触摸移动
			handleTouchMove(event) {
				event.preventDefault() // 防止页面滚动
			},

			// 触摸结束
			handleTouchEnd(event) {
				if (this.isSliding) return

				this.touchEndY = event.changedTouches[0].clientY
				const deltaY = this.touchStartY - this.touchEndY

				// 向下滑动超过50px就跳转
				if (deltaY > 50) {
					this.slideToThird()
				}
			},

			// 手动切换时钟图片
			switchClock() {
				if (this.clockImage === '/static/clock-one.png') {
					this.clockImage = '/static/clock-two.png'
				} else {
					this.clockImage = '/static/clock-one.png'
				}
			},

			// 滑动到第三页
			slideToThird() {
				if (this.isSliding) return

				this.isSliding = true
				this.showSlideHint = false // 隐藏提示

				// 添加滑动动画效果
				setTimeout(() => {
					// 跳转到第三页（这里先用返回首页代替）
					window.location.href = window.location.href.replace('/pages/second/second', '/pages/index/index');
				}, 300)
			}
		}
	}
</script>

<style>
.second-container {
	width: 100%;
	height: 100vh;
	background-color: rgb(227, 94, 54);
	display: flex;
	flex-direction: column;
	overflow: hidden;
	position: relative;
}

/* Logo样式 */
.logo-container {
	position: absolute;
	top: 40rpx;
	left: 0;
	right: 0;
	width: 100%;
	z-index: 10;
}

.logo-image {
	width: 100%;
	height: auto;
}

.clock-container {
	position: relative;
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	transform: translateX(-100vw) scale(0.3) rotate(-360deg);
	opacity: 0;
	transition: all 2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.clock-container.animate-in {
	transform: translateX(0) scale(1) rotate(0deg);
	opacity: 1;
}

.clock-image {
	width: 60vw;
	height: 60vw;
	max-width: 800rpx;
	max-height: 800rpx;
	filter: drop-shadow(0 40rpx 80rpx rgba(0, 0, 0, 0.3));
	animation: float3D 6s ease-in-out infinite;
	transition: all 0.3s ease;
}

/* 闪动文字样式 */
.clock-text {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 110;
	pointer-events: none;
}

.clock-text text {
	color: white;
	font-size: 40rpx;
	font-weight: bold;
	text-align: center;
	opacity: 0.8;
	animation: textBlink 3s ease-in-out infinite;
	text-shadow: 4rpx 4rpx 8rpx rgba(0, 0, 0, 0.8);
}

/* 下滑提示样式 */
.slide-hint {
	position: fixed;
	bottom: 100rpx;
	left: 50%;
	transform: translateX(-50%);
	text-align: center;
	opacity: 0;
	transition: all 0.5s ease;
	z-index: 120;
}

.slide-hint.show {
	opacity: 0.9;
}

.slide-arrow text {
	font-size: 60rpx;
	color: white;
	animation: slideArrowBounce 2s ease-in-out infinite;
}

.slide-text {
	margin-top: 20rpx;
}

.slide-text text {
	font-size: 24rpx;
	font-weight: bold;
	color: white;
	text-shadow: 4rpx 4rpx 8rpx rgba(0, 0, 0, 0.8);
}

/* 飘动图片样式 */
.floating-images {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
	z-index: 5;
}

.float-img {
	position: absolute;
	width: 160rpx;
	height: 160rpx;
	opacity: 0.8;
}

/* 每个图片的位置和动画 - 从底部飞入 */
.float-img-1 {
	left: 10%;
	animation: flyUp1 15s linear infinite;
}

.float-img-2 {
	right: 15%;
	animation: flyUp2 18s linear infinite;
}

.float-img-3 {
	left: 20%;
	animation: flyUp3 16s linear infinite;
}

.float-img-4 {
	right: 10%;
	animation: flyUp4 20s linear infinite;
}

.float-img-5 {
	left: 50%;
	transform: translateX(-50%);
	animation: flyUp5 14s linear infinite;
}

.float-img-6 {
	left: 5%;
	animation: flyUp6 19s linear infinite;
}

.float-img-7 {
	right: 5%;
	animation: flyUp7 17s linear infinite;
}

.float-img-8 {
	left: 50%;
	transform: translateX(-50%);
	animation: flyUp8 22s linear infinite;
}

/* 底部图片样式 */
.bottom-image {
	position: absolute;
	bottom: 40rpx;
	left: 0;
	right: 0;
	width: 100%;
	z-index: 10;
}

.end-logo {
	width: 100%;
	height: auto;
}

/* 文字闪动动画 */
@keyframes textBlink {
	0%, 100% {
		opacity: 0.8;
		transform: translate(-50%, -50%) scale(1);
	}
	50% {
		opacity: 0.3;
		transform: translate(-50%, -50%) scale(1.05);
	}
}

/* 下滑箭头弹跳动画 */
@keyframes slideArrowBounce {
	0%, 100% {
		transform: translateY(0rpx);
	}
	50% {
		transform: translateY(20rpx);
	}
}

/* 3D浮动动画 */
@keyframes float3D {
	0%, 100% {
		transform: translateY(0rpx) rotateY(0deg) rotateX(0deg) rotateZ(0deg);
	}
	25% {
		transform: translateY(-30rpx) rotateY(5deg) rotateX(2deg) rotateZ(1deg);
	}
	50% {
		transform: translateY(-50rpx) rotateY(0deg) rotateX(-2deg) rotateZ(-1deg);
	}
	75% {
		transform: translateY(-30rpx) rotateY(-5deg) rotateX(2deg) rotateZ(1deg);
	}
}

/* 从底部飞到顶部的动画关键帧 */
@keyframes flyUp1 {
	0% {
		top: 110%;
		opacity: 0;
		transform: rotate(0deg) scale(0.8);
	}
	10% {
		opacity: 1;
	}
	90% {
		opacity: 1;
	}
	100% {
		top: -10%;
		opacity: 0;
		transform: rotate(360deg) scale(1.2);
	}
}

@keyframes flyUp2 {
	0% {
		top: 110%;
		opacity: 0;
		transform: rotate(0deg) scale(0.8);
	}
	8% {
		opacity: 1;
	}
	92% {
		opacity: 1;
	}
	100% {
		top: -10%;
		opacity: 0;
		transform: rotate(-360deg) scale(1.2);
	}
}

@keyframes flyUp3 {
	0% {
		top: 110%;
		opacity: 0;
		transform: rotate(0deg) scale(0.8);
	}
	12% {
		opacity: 1;
	}
	88% {
		opacity: 1;
	}
	100% {
		top: -10%;
		opacity: 0;
		transform: rotate(180deg) scale(1.2);
	}
}

@keyframes flyUp4 {
	0% {
		top: 110%;
		opacity: 0;
		transform: rotate(0deg) scale(0.8);
	}
	7% {
		opacity: 1;
	}
	93% {
		opacity: 1;
	}
	100% {
		top: -10%;
		opacity: 0;
		transform: rotate(-180deg) scale(1.2);
	}
}

@keyframes flyUp5 {
	0% {
		top: 110%;
		opacity: 0;
		transform: translateX(-50%) rotate(0deg) scale(0.8);
	}
	15% {
		opacity: 1;
	}
	85% {
		opacity: 1;
	}
	100% {
		top: -10%;
		opacity: 0;
		transform: translateX(-50%) rotate(720deg) scale(1.2);
	}
}

@keyframes flyUp6 {
	0% {
		top: 110%;
		opacity: 0;
		transform: rotate(0deg) scale(0.8);
	}
	9% {
		opacity: 1;
	}
	91% {
		opacity: 1;
	}
	100% {
		top: -10%;
		opacity: 0;
		transform: rotate(270deg) scale(1.2);
	}
}

@keyframes flyUp7 {
	0% {
		top: 110%;
		opacity: 0;
		transform: rotate(0deg) scale(0.8);
	}
	11% {
		opacity: 1;
	}
	89% {
		opacity: 1;
	}
	100% {
		top: -10%;
		opacity: 0;
		transform: rotate(-270deg) scale(1.2);
	}
}

@keyframes flyUp8 {
	0% {
		top: 110%;
		opacity: 0;
		transform: translateX(-50%) rotate(0deg) scale(0.8);
	}
	6% {
		opacity: 1;
	}
	94% {
		opacity: 1;
	}
	100% {
		top: -10%;
		opacity: 0;
		transform: translateX(-50%) rotate(-720deg) scale(1.2);
	}
}
</style>
